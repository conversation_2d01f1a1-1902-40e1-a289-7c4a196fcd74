import csv
import logging
from itertools import islice
from typing import Dict, Iterable, List, Optional, Set, Tuple

from mobility.funky import ImmutableDict
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.mode_constraints import (
    Constraints,
    DurationConstraint,
    DurationConstraints,
)
from mobility.ir.study import (
    CsvEmployee,
    CsvSite,
    CsvStudy,
    CsvStudyDataParameter,
    PointOfInterest,
    ScenarioData,
)
from mobility.ir.study_types import Scenarios
from mobility.ir.transport import TransportMode

from .exceptions import WrongCSVFormatError


class CsvMultiReader:
    def __init__(
        self,
        site_csv_file: Iterable[str],
        employee_csv_file: Iterable[str],
        parameters_csv_file: Iterable[str],
    ) -> None:
        self.employee_id_column = "Id Interne"
        self.employee_address_column = "Adresses des employés"
        self.employee_mode_column = "Modes de transport des employés"
        self.employee_remote_column = "Télétravail"
        self.employee_current_site_column = "Site actuel"
        self.employee_minimal_header = {
            self.employee_id_column,
            self.employee_address_column,
            self.employee_mode_column,
            self.employee_current_site_column,
        }
        self.site_id_column = "Identifiant du site"
        self.site_address_column = "Adresses des lieux à analyser"
        self.site_parking_cost_column = "Prix du stationnement"
        self.site_minimal_header = {self.site_id_column, self.site_address_column}
        self.sites_data = list(site_csv_file)
        self.employees_data = list(employee_csv_file)
        self.parameters_data = list(parameters_csv_file)

    def build(self) -> CsvStudy:
        scenario_data_mapping = self.build_scenario_data_mapping()
        scenario_columns = list(scenario_data_mapping)
        employees_mapping = self.build_employees_mapping()
        sites_mapping = self.build_sites_mapping()
        scenarios = (
            self.build_raw_scenarios(scenario_columns)
            .mutate_scenarios(scenario_data_mapping)
            .mutate_origins(employees_mapping)
            .mutate_destinations(sites_mapping)
        )
        study_data_parameters = self.build_study_data_parameters()
        pois = self.build_pois()
        return CsvStudy(scenarios=scenarios, data=study_data_parameters, pois=pois)

    def build_study_data_parameters(self) -> CsvStudyDataParameter:
        return CsvParameterReader(self.parameters_data).parse_study_data()

    def build_pois(self) -> Tuple[PointOfInterest, ...]:
        if len(self.parameters_data) == 0:
            return tuple()
        return CsvParameterReader(self.parameters_data).build_point_of_interests()

    def build_scenario_data_mapping(self) -> Dict[str, ScenarioData]:
        reader = csv.reader(self.employees_data)
        employees_header = next(reader)
        after_current_site_header = (
            employees_header.index(self.employee_current_site_column) + 1
        )
        scenarios_names = employees_header[after_current_site_header:]
        scenarios = {
            self.employee_current_site_column: ScenarioData(
                id=1, nickname="Actuel", is_present=True
            )
        }
        for i, name in enumerate(scenarios_names, start=2):
            scenarios[name] = ScenarioData(id=i, nickname=name, is_present=False)
        return scenarios

    def build_employees_mapping(self) -> Dict[str, CsvEmployee]:
        employees = {}
        reader = csv.DictReader(self.employees_data)
        for i, row in enumerate(reader, start=1):
            employee = self._extract_employee(i, row)
            employees[employee.nickname] = employee
        if len(employees) == 0:
            raise WrongCSVFormatError("No employee data in CSV")
        return employees

    def build_sites_mapping(self) -> Dict[str, CsvSite]:
        sites = {}
        reader = csv.DictReader(self.sites_data)
        for i, row in enumerate(reader, start=1):
            site = self._extract_site(i, row)
            if site is None:
                break
            else:
                sites[site.nickname] = site
        if len(sites) == 0:
            raise WrongCSVFormatError("No site data in CSV")
        return sites

    def build_raw_scenarios(
        self, scenario_columns: List[str]
    ) -> Scenarios[str, str, str, None]:
        scenarios: Scenarios[str, str, str, None] = Scenarios.empty()
        reader = csv.DictReader(self.employees_data)
        for row in reader:
            employee: str = row[self.employee_id_column]
            if employee is None or employee == "":
                break
            employee_commutes = set()
            for scenario_column in scenario_columns:
                site: str = row[scenario_column]
                scenario_name: str = scenario_column
                employee_commutes.add((scenario_name, site, None))
            scenarios = Scenarios.add_origin(scenarios, employee, employee_commutes)
        return scenarios

    def _extract_site(self, site_id: int, row: Dict[str, str]) -> Optional[CsvSite]:
        raise_error_if_header_is_invalid(set(row.keys()), self.site_minimal_header)
        nickname = row[self.site_id_column]
        address = row[self.site_address_column]
        costs = row.get(self.site_parking_cost_column, None)
        parking_costs = _extract_site_parking_cost(costs)
        if nickname != "" and address != "":
            return CsvSite(
                id=site_id,
                nickname=nickname,
                address=address,
                car_parking_cost=parking_costs.get(TransportMode.CAR, None),
                bicycle_parking_cost=parking_costs.get(TransportMode.BICYCLE, None),
            )
        else:
            return None

    def _extract_employee(self, employee_id: int, row: Dict[str, str]) -> CsvEmployee:
        raise_error_if_header_is_invalid(set(row.keys()), self.employee_minimal_header)
        transport_mode = self._extract_transport_mode(row[self.employee_mode_column])
        remote = self._extract_remote(row.get(self.employee_remote_column))
        return CsvEmployee(
            id=employee_id,
            nickname=row[self.employee_id_column],
            address=row[self.employee_address_column],
            transport_mode=transport_mode,
            remote=remote,
        )

    @staticmethod
    def _extract_transport_mode(raw_mode: str) -> Optional[TransportMode]:
        try:
            return TransportMode.from_string(raw_mode)
        except ValueError:
            if raw_mode != "Inconnu":
                logging.error(f"Unknown mode {raw_mode}")
            return None

    @staticmethod
    def _extract_remote(raw_remote: Optional[str]) -> bool:
        return {
            "oui": True,
            "Oui": True,
            "OUI": True,
            "yes": True,
            "Yes": True,
            "YES": True,
            "non": False,
            "Non": False,
            "NON": False,
            "no": False,
            "No": False,
            "NO": False,
            None: False,
        }.get(raw_remote, False)


def raise_error_if_header_is_invalid(
    header: Set[str], expected_header: Set[str]
) -> None:
    missing_elements = expected_header - header
    if len(missing_elements) > 0:
        raise WrongCSVFormatError(
            f"Invalid header: expected {expected_header} but got {header},"
            f" missing {missing_elements}"
        )


def build_study_from_csv(
    employee_csv_file: Iterable[str],
    site_csv_file: Iterable[str],
    parameters_csv_file: Iterable[str],
) -> CsvStudy:
    return CsvMultiReader(
        site_csv_file=site_csv_file,
        employee_csv_file=employee_csv_file,
        parameters_csv_file=parameters_csv_file,
    ).build()


class CsvParameterReader:
    def __init__(self, parameters_csv_file: Iterable[str]) -> None:
        self.parameter_id_header = "Paramètre"
        self.parameter_value_header = "Valeur"
        self.parameter_minimal_header = {
            self.parameter_id_header,
            self.parameter_value_header,
        }
        self.poi_name_header = "Nom du point d'intérêt"
        self.poi_coordinates_header = "Coordonnées de localisation"
        self.NB_LINES_SKIP_FOR_POI = 10
        self.parameters = list(parameters_csv_file)

    def _extract_study_parameter(self, row: Dict[str, str]) -> Dict[str, str]:
        raise_error_if_header_is_invalid(
            set(row.keys()), {self.parameter_id_header, self.parameter_value_header}
        )
        parameter_name = row[self.parameter_id_header]
        parameter_value = row[self.parameter_value_header]
        return {parameter_name: parameter_value}

    def build_parameters_mapping(self) -> Dict[str, str]:
        reader = csv.DictReader(self.parameters)
        parameters = {}
        for row in reader:
            parameter = self._extract_study_parameter(row)
            if parameter == {}:
                break
            parameters.update(parameter)
        return parameters

    def build_point_of_interests(self) -> Tuple[PointOfInterest, ...]:
        reader = csv.DictReader(
            islice(self.parameters, self.NB_LINES_SKIP_FOR_POI, None)
        )
        pois = []
        for i, row in enumerate(reader, start=1):
            try:
                poi_name = row[self.poi_name_header]
                poi_coords_str = row[self.poi_coordinates_header]
                coordinates = GeoCoordinates.from_string(poi_coords_str)
                pois.append(PointOfInterest(name=poi_name, coordinates=coordinates))
            except ValueError:
                raise WrongCSVFormatError(
                    f"POI n°{i} has invalid coordinates {poi_coords_str}"
                )
            except KeyError:
                raise WrongCSVFormatError(
                    f"POI n°{i} has missing headers "
                    f"{self.poi_name_header} or {self.poi_coordinates_header}"
                )
        return tuple(pois)

    def _parse_arrival_time(self, time_str: str) -> Tuple[int, int]:
        try:
            hour, minute, *_ = map(int, time_str.split(":"))
            return hour, minute
        except ValueError:
            raise WrongCSVFormatError(
                f"Invalid arrival time format: expected HH:MM or HH:MM:SS but got {time_str}"
            )

    def _extract_max_durations(
        self, parameters_mapping: Dict[str, str]
    ) -> DurationConstraints:
        duration_constraints = {}

        params_to_mode_mapping = {
            "Temps maximal acceptable - marche (en minutes)": TransportMode.WALK,
            "Temps maximal acceptable - vélo (en minutes)": TransportMode.BICYCLE,
            "Temps maximal acceptable - TC (en minutes)": TransportMode.PUBLIC_TRANSPORT,
        }

        for parameter_name, mode in params_to_mode_mapping.items():
            duration_value = parameters_mapping.get(parameter_name)
            if duration_value is not None:
                try:
                    max_duration_minutes = int(duration_value)
                    duration_constraints[mode] = DurationConstraint(
                        mode, max_duration_minutes
                    )
                except ValueError:
                    raise WrongCSVFormatError(
                        f"Invalid duration value for {parameter_name}: {duration_value}"
                    )

        return DurationConstraints(constraints=ImmutableDict(duration_constraints))

    def _extract_constraints(self, parameters_mapping: Dict[str, str]) -> Constraints:
        duration_constraints = self._extract_max_durations(parameters_mapping)
        max_transfers_str = parameters_mapping.get(
            "Nombre de correspondances maximal en transport en commun", None
        )
        max_transfers = None
        if max_transfers_str is not None:
            try:
                max_transfers = int(max_transfers_str)
            except ValueError:
                raise WrongCSVFormatError(
                    f"Invalid value for maximum transfers: {max_transfers_str}"
                )
        return Constraints(duration=duration_constraints, max_transfers=max_transfers)

    def parse_study_data(self) -> CsvStudyDataParameter:
        parameters_mapping = self.build_parameters_mapping()
        company = parameters_mapping.get("Client", "[Nom du client]")
        mission_id = parameters_mapping.get("Numéro d'affaire", "XXMYYY.V")
        arrival_time_str = parameters_mapping.get("Heure d'arrivée", None)
        agency = parameters_mapping.get("Agence", "")
        constraints = self._extract_constraints(parameters_mapping)

        if arrival_time_str:
            arrival_time: Tuple[int, int] = self._parse_arrival_time(arrival_time_str)
        else:
            arrival_time = (8, 30)

        return CsvStudyDataParameter(
            company=company,
            mission_id=mission_id,
            arrival_time=arrival_time,
            agency=agency,
            constraints=constraints,
        )


def _extract_site_parking_cost(costs: Optional[str]) -> Dict[TransportMode, float]:
    """# costs input is written as vélo:5, voiture:10"""
    if costs is None or costs == "":
        return {}
    try:
        return {
            TransportMode.from_string(mode.strip()): float(cost.strip())
            for mode, cost in (item.split(":") for item in costs.split(","))
        }
    except ValueError:
        raise WrongCSVFormatError(
            f"Invalid parking cost format: expected a number but got {costs}"
        )
