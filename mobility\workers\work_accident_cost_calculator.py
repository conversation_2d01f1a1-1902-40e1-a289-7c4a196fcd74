from typing import List

from mobility.ir.cost import Cost, Cost<PERSON><PERSON>, CostPayer
from mobility.quantity import Quantity, euros_2022, kilometers, trip

WALK_SOCIETY_WORK_ACCIDENT_COST = 0.2 * euros_2022 / kilometers
BICYCLE_SOCIETY_WORK_ACCIDENT_COST = 0.23 * euros_2022 / kilometers
PT_SOCIETY_WORK_ACCIDENT_COST = 0.002 * euros_2022 / kilometers
CAR_SOCIETY_WORK_ACCIDENT_COST = 0.02 * euros_2022 / kilometers
WALK_COMPANY_WORK_ACCIDENT_COST = 0.022 * euros_2022 / kilometers
BICYCLE_COMPANY_WORK_ACCIDENT_COST = 0.02 * euros_2022 / kilometers
PT_COMPANY_WORK_ACCIDENT_COST = 0.0 * euros_2022 / kilometers
CAR_COMPANY_WORK_ACCIDENT_COST = 0.002 * euros_2022 / kilometers


class WorkAccidentCostCalculator:
    def compute_walk_work_accident_cost_for_society(
        self, distance: Quantity
    ) -> Quantity:
        return distance * WALK_SOCIETY_WORK_ACCIDENT_COST / trip

    def compute_bicycle_work_accident_cost_for_society(
        self, distance: Quantity
    ) -> Quantity:
        return distance * BICYCLE_SOCIETY_WORK_ACCIDENT_COST / trip

    def compute_pt_work_accident_cost_for_society(self, distance: Quantity) -> Quantity:
        return distance * PT_SOCIETY_WORK_ACCIDENT_COST / trip

    def compute_car_work_accident_cost_for_society(
        self, distance: Quantity
    ) -> Quantity:
        return distance * CAR_SOCIETY_WORK_ACCIDENT_COST / trip

    def compute_walk_work_accident_cost_for_company(
        self, distance: Quantity
    ) -> Quantity:
        return distance * WALK_COMPANY_WORK_ACCIDENT_COST / trip

    def compute_bicycle_work_accident_cost_for_company(
        self, distance: Quantity
    ) -> Quantity:
        return distance * BICYCLE_COMPANY_WORK_ACCIDENT_COST / trip

    def compute_pt_work_accident_cost_for_company(self, distance: Quantity) -> Quantity:
        return distance * PT_COMPANY_WORK_ACCIDENT_COST / trip

    def compute_car_work_accident_cost_for_company(
        self, distance: Quantity
    ) -> Quantity:
        return distance * CAR_COMPANY_WORK_ACCIDENT_COST / trip

    def compute_walk_accident_cost(self, distance: Quantity) -> Quantity:
        return self.compute_walk_work_accident_cost_for_company(
            distance
        ) + self.compute_walk_work_accident_cost_for_society(distance)

    def compute_bicycle_accident_cost(self, distance: Quantity) -> Quantity:
        return self.compute_bicycle_work_accident_cost_for_company(
            distance
        ) + self.compute_bicycle_work_accident_cost_for_society(distance)

    def compute_pt_accident_cost(self, distance: Quantity) -> Quantity:
        return self.compute_pt_work_accident_cost_for_company(
            distance
        ) + self.compute_pt_work_accident_cost_for_society(distance)

    def compute_car_accident_cost(self, distance: Quantity) -> Quantity:
        return self.compute_car_work_accident_cost_for_company(
            distance
        ) + self.compute_car_work_accident_cost_for_society(distance)

    def compute_car_accidents_costs(self, distance: Quantity) -> List[Cost]:
        return [
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=self.compute_car_work_accident_cost_for_company(distance),
            ),
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=self.compute_car_work_accident_cost_for_society(distance),
            ),
        ]
