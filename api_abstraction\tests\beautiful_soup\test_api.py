from typing import Dict, Optional, <PERSON><PERSON>
from unittest import mock

from bs4 import BeautifulSoup

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.beautiful_soup.api import (
    BeautifulSoupTravelTimeAPI,
    JourneyAttribute,
    TravelTimeApi,
)
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


def trivial_url_maker(
    origin: GeoCoordinates,
    destination: GeoCoordinates,
    mode: TransportMode,
    arrival_time: Tuple[int, int],
):
    return "trivial_url"


def trivial_soup_journey_parser(soup: BeautifulSoup) -> JourneyAttribute:
    return JourneyAttribute(distance=100, duration=200, emission=300)


class TestBeautifulSoupTravelTimeApi:
    def test_should_init_api(self) -> None:
        api = BeautifulSoupTravelTimeAPI(
            EventReporter(), trivial_url_maker, trivial_soup_journey_parser
        )

        assert isinstance(api, TravelTimeApi)

    def test_should_chain_url_maker_and_journey_parser(self) -> None:
        api = BeautifulSoupTravelTimeAPI(
            EventReporter(), trivial_url_maker, trivial_soup_journey_parser
        )
        origin = GeoCoordinates(0.0, 2.0)
        destination = GeoCoordinates(0.0, 2.0)
        mode = TransportMode.PUBLIC_TRANSPORT
        arrival_time = (8, 30)

        def check_call_bs(url: str, post_data: Optional[Dict] = None) -> BeautifulSoup:
            assert url == "trivial_url"
            assert post_data is None
            return BeautifulSoup("")

        api._beautiful_soup_caller = check_call_bs

        journey_attributes = api.time(origin, destination, mode, arrival_time)

        assert journey_attributes == JourneyAttribute(
            distance=100, duration=200, emission=300
        )

    def test_should_chain_url_maker_data_maker_and_journey_parser(self) -> None:
        mock_url_maker = mock.Mock()
        mock_post_data_maker = mock.Mock()
        mock_journey_parser = mock.Mock()
        api = BeautifulSoupTravelTimeAPI(
            EventReporter(), mock_url_maker, mock_journey_parser, mock_post_data_maker
        )
        origin = GeoCoordinates(0.0, 2.0)
        destination = GeoCoordinates(0.0, 2.0)
        mode = TransportMode.PUBLIC_TRANSPORT
        arrival_time = (8, 30)
        mock_bs_caller = mock.Mock()
        api._beautiful_soup_caller = mock_bs_caller
        mock_ban_france = mock.Mock()
        api.ban_france = mock_ban_france

        api.time(origin, destination, mode, arrival_time)

        mock_url_maker.assert_called_with(origin, destination, mode, arrival_time)
        mock_ban_france.reverse.assert_has_calls(
            [mock.call(origin), mock.call(destination)]
        )
        mock_post_data_maker.assert_called_with(
            mock_ban_france.reverse(origin),
            mock_ban_france.reverse(destination),
            mode,
            arrival_time,
        )
        mock_bs_caller.assert_called_with(
            mock_url_maker.return_value, mock_post_data_maker.return_value
        )
