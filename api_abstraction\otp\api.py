import datetime
import json
import logging
from itertools import product
from typing import Dict, List, Optional, Tuple

import geopandas
import requests
import shapely
from shapely.geometry import shape

from api_abstraction.api.api import ApiInapt
from api_abstraction.api.date_iterators import DateTimeInterval
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.hypotheses import closest_tuesday_8_30_in_interval
from api_abstraction.api.travel_time_api import ApiFail, JourneyAttribute, TravelTimeApi
from mobility.constants import WALK_SPEED_METERS_PER_SECOND
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import meters
from mobility.workers.distance_computer import compute_distance


class OTPTravelTimeAPI(TravelTimeApi):
    max_call_per_period = 1000000
    period_in_seconds = 0.001
    retries = 10
    retry_delay_in_seconds = 0.0
    root_url = "http://localhost:8080/otp/"
    default_router_url = root_url + "routers/default"
    trip_planning_url = root_url + "routers/default/plan"
    isochrone_url = root_url + "routers/default/isochrone"
    max_transfers = None

    def __init__(self, token: str, reporter: EventReporter) -> None:
        super().__init__(token, reporter)
        self.request_date: Optional[datetime.datetime] = None
        self.logger = logging.getLogger("api")
        self.logger.info("Initialized OTP")

    def get_request_date(self) -> datetime.datetime:
        if self.request_date is None:
            self.request_date = self._compute_request_date()
        return self.request_date

    def _emission_per_mode(
        self, requested_mode: TransportMode, reported_otp_mode: str
    ) -> float:
        if reported_otp_mode in ["SUBWAY", "FUNICULAR"]:
            # based on fr:metro emissions
            return 3.8
        elif reported_otp_mode in ["TRAM", "GONDOLA"]:
            # based on fr:tramway emissions
            return 3.1
        elif reported_otp_mode in ["RAIL", "FERRY"]:
            # based on fr:TER emissions
            return 29.0
        elif reported_otp_mode == "BUS":
            return 103.0
        elif reported_otp_mode == "WALK":
            return 0.0
        elif reported_otp_mode == "BICYCLE":
            match requested_mode:
                case TransportMode.ELECTRIC_BICYCLE | TransportMode.FAST_BICYCLE:
                    return 2.23
                case TransportMode.BICYCLE | TransportMode.BICYCLE_PUBLIC_TRANSPORT:
                    return 0.0
                case _:
                    raise ValueError(
                        f"Mode {reported_otp_mode} present in route while requesting mode {requested_mode}"
                    )
        elif reported_otp_mode == "CAR":
            match requested_mode:
                case TransportMode.MOTORCYCLE:
                    return 165.0
                case TransportMode.CARPOOLING:
                    return 96.0
                case TransportMode.ELECTRIC_CAR | TransportMode.ELECTRIC_MOTORCYCLE:
                    return 19.8
                case TransportMode.CAR | TransportMode.CAR_PUBLIC_TRANSPORT:
                    return 193.2
                case _:
                    raise ValueError(
                        f"Mode {reported_otp_mode} present in route while requesting mode {requested_mode}"
                    )
        else:
            raise ValueError(
                f"Mode {reported_otp_mode} is not handled in emission computation"
            )

    def _mode_to_otp_mode(self, mode: TransportMode) -> str:
        mapping = {
            TransportMode.PUBLIC_TRANSPORT: "TRANSIT,WALK",
            TransportMode.CAR: "CAR,WALK",
            TransportMode.CARPOOLING: "CAR,WALK",
            TransportMode.MOTORCYCLE: "CAR,WALK",
            TransportMode.ELECTRIC_CAR: "CAR,WALK",
            TransportMode.WALK: "WALK",
            TransportMode.BICYCLE: "BICYCLE",
            TransportMode.ELECTRIC_BICYCLE: "BICYCLE",
            TransportMode.ELECTRIC_MOTORCYCLE: "CAR,WALK",
            TransportMode.FAST_BICYCLE: "BICYCLE",
            TransportMode.CAR_PUBLIC_TRANSPORT: "CAR,WALK,TRANSIT",  # Kiss&ride: e.g. car deposit at train station
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: "TRANSIT,BICYCLE",
        }
        if mode not in mapping:
            raise ApiInapt(f"OTP Api does not handle mode {mode}")
        return mapping[mode]

    def _mode_to_bike_speed(self, mode: TransportMode) -> float:
        if mode == TransportMode.ELECTRIC_BICYCLE:
            return 5.6  # 20 km/h = 5.555... m/s
        elif mode == TransportMode.FAST_BICYCLE:
            return 12.5  # 45 km/h = 12.5 m/s
        else:
            return 4.17  # 15km/h = 4.1666... m/s

    def _get_isochrone_fudge_delta_time(self, mode: TransportMode) -> int:
        return {
            TransportMode.PUBLIC_TRANSPORT: 0,
            TransportMode.CAR: 0 * 60,
            TransportMode.WALK: 0 * 60,
            TransportMode.BICYCLE: 0 * 60,
            TransportMode.CARPOOLING: 0,
            TransportMode.MOTORCYCLE: 0,
            TransportMode.ELECTRIC_CAR: 0,
            TransportMode.ELECTRIC_BICYCLE: 0,
            TransportMode.ELECTRIC_MOTORCYCLE: 0,
            TransportMode.FAST_BICYCLE: 0,
            TransportMode.CAR_PUBLIC_TRANSPORT: 0,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0,
        }[mode]

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        if mode == TransportMode.CAR:
            delta_deg = [0.0, -0.005, 0.005]
        else:
            delta_deg = [0.0]
        return self._time_around(
            origin, destination, mode, arrival_time, delta_deg, max_transfers
        )

    def _time_around(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        delta_deg: List[float],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        coordinates_to_try = self.jitter_coordinates(origin, destination, delta_deg)
        for offset_origin, offset_destination in coordinates_to_try:
            try:
                return self._time_at(
                    offset_origin, offset_destination, mode, arrival_time, max_transfers
                )
            except ApiInapt:
                raise
            except ApiFail as e:
                last_failure = e
                continue
        raise ApiFail(str(last_failure))

    def _time_at(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        request_date = self.get_request_date()
        params = {
            "arriveBy": True,
            "date": request_date.date(),
            "fromPlace": f"{origin.latitude},{origin.longitude}",
            "toPlace": f"{destination.latitude},{destination.longitude}",
            "time": self._compute_arrival_request_time(arrival_time),
            "walkSpeed": WALK_SPEED_METERS_PER_SECOND,
            "maxWalkDistance": WALK_SPEED_METERS_PER_SECOND * 1.5 * 60 * 60,
        }
        params = self._add_mode_params(params, mode)
        response = self._call_requests(url=self.trip_planning_url, params=params)
        try:
            self._check_plan_response_validity(response)
        except ApiInapt as e:
            if e.message == "TOO_CLOSE":
                return JourneyAttribute(
                    duration=1,
                    distance=1,
                    emission=None,
                )
            else:
                raise

        plan = response["plan"]
        if mode == TransportMode.PUBLIC_TRANSPORT and max_transfers is not None:
            plan = self._filter_itineraries_above_max_transfers(plan, max_transfers)

        itineraries = plan["itineraries"]
        for i, itinerary in enumerate(itineraries):
            self.logger.debug(
                f"Route {i+1}: Departure time: {itinerary['startTime']}, "
                f"Arrival time: {itinerary['endTime']}, "
                f"Travel time: {itinerary['duration']}"
            )
        itinerary = self._get_itinerary(plan, arrival_time)
        self.logger.debug(
            f"Chosen route: Departure time: {itinerary['startTime']}, "
            f"Arrival time: {itinerary['endTime']}, "
            f"Travel time: {itinerary['duration']}"
        )
        duration = itinerary["duration"]
        distance, emission = 0.0, 0.0
        for leg in itinerary["legs"]:
            distance += leg["distance"]
            emission += self._emission_per_mode(mode, leg["mode"]) * distance / 1000.0
        fudge_factor: Dict[TransportMode, float] = {}
        return JourneyAttribute(
            duration=duration * fudge_factor.get(mode, 1.0),
            distance=int(distance),
            emission=int(emission),
        )

    def _filter_itineraries_above_max_transfers(
        self, plan: Dict, max_transfers: int
    ) -> Dict:
        filtered_itineraries = [
            itinerary
            for itinerary in plan["itineraries"]
            if itinerary.get("transfers", 0) <= max_transfers
        ]

        if len(filtered_itineraries) == 0:
            filtered_itineraries = plan["itineraries"]

        return {**plan, "itineraries": filtered_itineraries}

    def _add_mode_params(self, params: Dict, mode: TransportMode) -> Dict:
        new_params = params.copy()
        new_params["mode"] = self._mode_to_otp_mode(mode)
        new_params["bikeSpeed"] = self._mode_to_bike_speed(mode)
        return new_params

    def _get_fastest_itinerary(self, plan: Dict) -> Dict:
        return min(plan["itineraries"], key=lambda itinerary: itinerary["duration"])

    def _get_itinerary(self, plan: Dict, arrival_time: Tuple[int, int]) -> Dict:
        fastest_itinerary = self._get_fastest_itinerary(plan)
        request_date = self.get_request_date()
        arrival_time_request = self._compute_arrival_request_time(arrival_time)
        arrival_datetime = datetime.datetime.combine(
            request_date.date(), arrival_time_request
        )
        itinerary_start_datetime = datetime.datetime.fromtimestamp(
            fastest_itinerary["startTime"] / 1000.0
        )
        if (arrival_datetime - itinerary_start_datetime).total_seconds() <= 3 * 60 * 60:
            return fastest_itinerary
        return plan["itineraries"][0]

    def _compute_arrival_request_time(
        self, arrival_time: Tuple[int, int]
    ) -> datetime.time:
        hour, minute = arrival_time
        arrival_time_request = datetime.time(hour, minute)
        return arrival_time_request

    @staticmethod
    def jitter_coordinates(
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        delta_deg: List[float],
    ) -> List[Tuple[GeoCoordinates, GeoCoordinates]]:
        deltas = sorted(
            product(delta_deg, delta_deg, delta_deg, delta_deg),
            key=lambda deltas: sum(abs(d) for d in deltas),
        )
        return [
            (
                GeoCoordinates(
                    latitude=origin.latitude + odx,
                    longitude=origin.longitude + ody,
                ),
                GeoCoordinates(
                    latitude=destination.latitude + ddx,
                    longitude=destination.longitude + ddy,
                ),
            )
            for ddx, ddy, odx, ody in deltas
        ]

    def _check_plan_response_validity(self, response: Dict) -> None:
        if "plan" not in response:
            if "error" in response:
                if "message" in response["error"]:
                    if response["error"]["message"] == "TOO_CLOSE":
                        raise ApiInapt("TOO_CLOSE")
                message = f"{response['error']}"
            else:
                message = f"No plan in response {response}"
            raise ApiFail(message)

    def _compute_request_date(self) -> datetime.datetime:
        try:
            default_router = self._call_requests(self.default_router_url, {})
        except ConnectionError as e:
            raise ApiFail(
                f"Unable to connect to {self.default_router_url}, "
                f"did you start the OTP server ?\n{e}"
            )
        service_start = default_router["transitServiceStarts"]
        try:
            start_date = datetime.datetime.fromtimestamp(service_start)
        except OSError as e:
            raise ValueError(
                f"Could not extract start date from <{service_start}>\n{e}"
            )
        end_date = datetime.datetime.fromtimestamp(default_router["transitServiceEnds"])
        return closest_tuesday_8_30_in_interval(
            DateTimeInterval(start_date, end_date), self._get_today()
        )

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        fudged_boundary = boundary + self._get_isochrone_fudge_delta_time(
            transport_mode
        )
        if transport_mode in [
            TransportMode.PUBLIC_TRANSPORT,
            TransportMode.CAR_PUBLIC_TRANSPORT,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT,
        ]:
            all_arrive_by = [True, False]
            all_hour = [7, 8, 9]
            all_minute = list(range(0, 60, 5))
        else:
            all_arrive_by = [False]
            all_hour = [8]
            all_minute = [30]
        iso_union = None
        for arrive_by in all_arrive_by:
            for hour in all_hour:
                for minute in all_minute:
                    geometry = self._compute_isochrone_with_departure_time(
                        territory,
                        destination,
                        transport_mode,
                        fudged_boundary,
                        arrive_by,
                        hour,
                        minute,
                    )
                    if iso_union is None:
                        iso_union = geometry.buffer(0)
                    else:
                        iso_union = iso_union.union(geometry.buffer(0)).simplify(
                            0.00001
                        )
        if iso_union is None:
            return {}
        smoothed_shape = self._smooth_shape_geographically(iso_union)
        return json.loads(geopandas.GeoSeries(smoothed_shape).to_json())["features"][0][
            "geometry"
        ]

    def _smooth_shape_geographically(self, coarse_shape: shape) -> shape:
        min_x, min_y, max_x, max_y = shapely.bounds(coarse_shape)
        middle_x = (max_x + min_x) / 2
        middle_y = (max_y + min_y) / 2
        middle_point = GeoCoordinates(latitude=middle_y, longitude=middle_x)
        x_point = GeoCoordinates(latitude=middle_y, longitude=middle_x + 0.1)
        y_point = GeoCoordinates(latitude=middle_y + 0.1, longitude=middle_x)
        x_distance = float(compute_distance(middle_point, x_point) / meters)
        y_distance = float(compute_distance(middle_point, y_point) / meters)
        x_factor = x_distance * 10.0
        y_factor = y_distance * 10.0
        projected_shape = shapely.transform(
            coarse_shape, lambda x: x * [x_factor, y_factor]
        )
        min_x, min_y, max_x, max_y = shapely.bounds(projected_shape)
        smoothed_shape = (
            projected_shape.buffer(200).buffer(-200).buffer(-50).buffer(100).simplify(5)
        )
        unprojected_smooth_shape = shapely.transform(
            smoothed_shape, lambda x: x * [1 / x_factor, 1 / y_factor]
        )
        return unprojected_smooth_shape

    def _compute_isochrone_with_departure_time(
        self,
        territory: Optional[Territory],
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
        arrive_by: bool,
        hour: int,
        minute: int = 0,
        second: int = 0,
    ) -> shape:
        request_date = self.get_request_date()
        params = {
            "arriveBy": arrive_by,
            "date": request_date.date(),
            "fromPlace": f"{destination.latitude},{destination.longitude}",
            "toPlace": f"{destination.latitude},{destination.longitude}",
            "time": datetime.time(hour, minute, second),
            "walkSpeed": WALK_SPEED_METERS_PER_SECOND,
            "cutoffSec": boundary,
            "walkBoardCost": 0,
            "alightSlack": 0,
            "boardSlack": 0,
            "transferSlack": 0,
            # "maxWalkDistance": 1000,
        }
        params = self._add_mode_params(params, transport_mode)
        if self.max_transfers is not None:
            params["maxTransfers"] = self.max_transfers
        response = self._call_requests(url=self.isochrone_url, params=params)
        return (
            shape(response["features"][0]["geometry"]).buffer(0.00001).simplify(0.00001)
        )

    def _call_requests(self, url: str, params: Dict) -> Dict:
        response = requests.get(url, params=params)
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError:
            print(response.text)
            raise


if __name__ == "__main__":
    api = OTPTravelTimeAPI(
        token="",
        reporter=EventReporter(),
    )
    origin = GeoCoordinates(latitude=43.606655464726636, longitude=1.4744256199237438)
    destination = GeoCoordinates(
        latitude=43.6661428876409, longitude=1.5040260552089648
    )
    mode = TransportMode.PUBLIC_TRANSPORT
    arrival_time = (8, 30)
    try:
        journey = api._time(origin, destination, mode, arrival_time, max_transfers=7)
        print(f"Journey time: {journey.duration} seconds")
    except ApiFail as e:
        print(f"API request failed: {str(e)}")
    except ApiInapt as e:
        print(f"API inapt: {str(e)}")
