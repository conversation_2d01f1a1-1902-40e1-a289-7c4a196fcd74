from functools import cache
from typing import List, Optional, Tuple

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


class FastestChoiceTravelTimeApi(TravelTimeApi):
    def __init__(self, availables_apis: List[TravelTimeApi]):
        self.availables_apis = availables_apis

    @cache
    def time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        journey_attributes = []
        for api in self.availables_apis:
            try:
                journey_attributes.append(
                    api.time(origin, destination, mode, arrival_time, max_transfers)
                )
            except (ApiInapt, ApiFail):
                continue
        if len(journey_attributes) == 0:
            raise ApiFail("No recorded API was apt")
        return min(journey_attributes, key=lambda a: a.duration)
