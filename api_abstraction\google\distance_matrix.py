from typing import Any, Dict, Optional

import googlemaps

from api_abstraction.api.travel_time_api import ApiFail, ApiTimeout, JourneyAttribute
from api_abstraction.google.base_google_api import (
    GoogleClientAPI,
    GoogleRequestParameters,
)
from mobility.ir.transport import TransportMode


class DistanceMatrix(GoogleClientAPI):
    def __init__(self, token: str) -> None:
        super().__init__()
        self.token = token
        self.google_routing_client = googlemaps.Client(token)
        self.ok_status = "OK"

    def compute_route(self, parameters: GoogleRequestParameters) -> JourneyAttribute:
        req_parameters = self._format_input_parameters(parameters)
        try:
            route_result = self.google_routing_client.distance_matrix(**req_parameters)
        except googlemaps.exceptions.Timeout as e:
            raise ApiTimeout(str(e))
        except (
            googlemaps.exceptions.ApiError,
            googlemaps.exceptions.TransportError,
            googlemaps.exceptions.HTTPError,
            googlemaps.exceptions._RetriableRequest,
            googlemaps.exceptions._OverQueryLimit,
        ) as e:
            raise ApiFail(str(e))
        return self._format_result(route_result, parameters.mode)

    def _format_result(
        self,
        result: Dict,
        mode: Optional[TransportMode],
    ) -> JourneyAttribute:
        if result["status"] != self.ok_status:
            raise ApiFail(f"Invalid response: status not ok in\n{result}")
        if len(result["rows"]) != 1:
            raise ApiFail(f"Invalid response: wrong row count {len(result['rows'])}")
        if len(result["rows"][0]["elements"]) != 1:
            raise ApiFail(
                "Invalid response: wrong elements count "
                f"{len(result['rows'][0]['elements'])}"
            )
        element = result["rows"][0]["elements"][0]
        if element["status"] != self.ok_status:
            raise ApiFail(f"Invalid response: element status not ok in\n{result}")
        return JourneyAttribute(
            duration=self._extract_duration_from_element(mode, element),
            distance=self._extract_distance_from_element(element),
            emission=None,
        )

    def _extract_duration_from_element(
        self, mode: Optional[TransportMode], element: Dict
    ) -> int:
        if mode is None or mode not in self.car_like_modes:
            return element["duration"]["value"]
        else:
            if "duration_in_traffic" in element:
                return element["duration_in_traffic"]["value"]
            else:
                raise ApiTimeout("Duration in traffic not computed")

    def _extract_distance_from_element(self, element: Dict) -> int:
        return element["distance"]["value"]

    def _format_input_parameters(
        self, parameters: GoogleRequestParameters
    ) -> Dict[str, Any]:
        formatted_request_parameters: Dict[str, Any] = {
            "origins": [(parameters.origin.latitude, parameters.origin.longitude)],
            "destinations": [
                (parameters.destination.latitude, parameters.destination.longitude)
            ],
            "mode": self._map_google_mode(parameters.mode),
            "departure_time": parameters.departure_time,
            "arrival_time": parameters.arrival_time,
            "traffic_model": parameters.traffic_model,
            "language": "fr",
            "units": "metric",
        }
        return formatted_request_parameters

    def _map_google_mode(self, mode: TransportMode) -> str:
        mode_mapping = {
            TransportMode.WALK: "walking",
            TransportMode.PUBLIC_TRANSPORT: "transit",
            TransportMode.CAR: "driving",
            TransportMode.ELECTRIC_CAR: "driving",
            TransportMode.MOTORCYCLE: "driving",
            TransportMode.ELECTRIC_MOTORCYCLE: "driving",
            TransportMode.CARPOOLING: "driving",
            TransportMode.BICYCLE: "bicycling",
        }
        if mode not in mode_mapping:
            raise ApiFail(f"Mode {mode.value} unavailable in mapping")
        return mode_mapping[mode]
