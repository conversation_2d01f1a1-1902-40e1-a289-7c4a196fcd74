#!/usr/bin/env python3
"""
4-Level Aggregation/Disaggregation Sankey Diagram for Strasbourg Mobility Flows
Demonstrates hierarchical flow visualization with aggregation→disaggregation pattern.
"""

import os
import pandas as pd
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher


def create_strasbourg_aggregation_flow():
    """Create separate charts showing the complete aggregation/disaggregation story."""

    # CSV file path - use the file in the workspace
    csv_path = "hv_aggregated_od_matrix_total_RN4_Est_Ouest.csv"

    # Output directory
    output_dir = "output_flow_charts"
    os.makedirs(output_dir, exist_ok=True)

    # Read CSV data
    print(f"Reading CSV data from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows of flow data")

    # Helper function for department mapping
    def get_department(zone_name):
        if zone_name.startswith("Strasbourg"):
            return "Strasbourg"
        else:
            return zone_name

    # Create department-level aggregated flows
    df_dept = df.copy()
    df_dept['origin_dept'] = df_dept['origin_zone'].apply(get_department)
    df_dept['destination_dept'] = df_dept['destination_zone'].apply(get_department)
    dept_flows = df_dept.groupby(['origin_dept', 'destination_dept'])['count'].sum().reset_index()

    # Chart 1: Strasbourg Communes → Strasbourg Department (Level 1→2)
    strasbourg_origins = df[df['origin_zone'].str.startswith('Strasbourg')].copy()
    level1_2_data = strasbourg_origins.groupby('origin_zone')['count'].sum().reset_index()

    level1_2_flows = []
    for _, row in level1_2_data.iterrows():
        level1_2_flows.extend([("Strasbourg", row['origin_zone'])] * row['count'])

    # Chart 2: Department Level Flows (Level 2→3)
    level2_3_flows = []
    for _, row in dept_flows.iterrows():
        level2_3_flows.extend([(row['origin_dept'], row['destination_dept'])] * row['count'])

    # Chart 3: Strasbourg Department → Strasbourg Communes (Level 3→4)
    strasbourg_destinations = df[df['destination_zone'].str.startswith('Strasbourg')].copy()
    level3_4_data = strasbourg_destinations.groupby('destination_zone')['count'].sum().reset_index()

    level3_4_flows = []
    for _, row in level3_4_data.iterrows():
        level3_4_flows.extend([("Strasbourg", row['destination_zone'])] * row['count'])

    # Create separate charts
    grapher = FlowChartGrapher[str](output_dir)

    # Generate colors and categories for each chart
    all_strasbourg = sorted([z for z in df['origin_zone'].unique() if z.startswith('Strasbourg')])
    all_departments = sorted(dept_flows['origin_dept'].unique())

    strasbourg_colors = {"Strasbourg": "#FF9800"}
    for i, commune in enumerate(all_strasbourg):
        strasbourg_colors[commune] = ["#E53935", "#1E88E5", "#43A047", "#9C27B0", "#FF5722"][i % 5]

    dept_colors = {}
    for i, dept in enumerate(all_departments):
        if dept == "Strasbourg":
            dept_colors[dept] = "#FF9800"
        else:
            dept_colors[dept] = ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#00BCD4"][i % 5]

    # Chart 1: Communes → Department
    chart1 = grapher.make_chart(
        "strasbourg_1_communes_to_dept",
        "Niveau 1→2: Communes Strasbourg vers Département",
        level1_2_flows,
        ("Communes Strasbourg", "Département Strasbourg"),
        strasbourg_colors,
        all_strasbourg + ["Strasbourg"],
        {k: k.replace("Strasbourg ", "") for k in all_strasbourg + ["Strasbourg"]},
        {}
    )

    # Chart 2: Department → Department
    chart2 = grapher.make_chart(
        "strasbourg_2_dept_to_dept",
        "Niveau 2→3: Flux Inter-Départements",
        level2_3_flows,
        ("Départements Origine", "Départements Destination"),
        dept_colors,
        all_departments,
        {k: k for k in all_departments},
        {}
    )

    # Chart 3: Department → Communes
    chart3 = grapher.make_chart(
        "strasbourg_3_dept_to_communes",
        "Niveau 3→4: Département vers Communes Strasbourg",
        level3_4_flows,
        ("Département Strasbourg", "Communes Strasbourg"),
        strasbourg_colors,
        ["Strasbourg"] + all_strasbourg,
        {k: k.replace("Strasbourg ", "") for k in ["Strasbourg"] + all_strasbourg},
        {}
    )

    print(f"Three-part aggregation charts generated:")
    print(f"  1. Communes→Department: {chart1}")
    print(f"  2. Department→Department: {chart2}")
    print(f"  3. Department→Communes: {chart3}")

    return [chart1, chart2, chart3]

"""
def create_strasbourg_aggregation_flow():

    # CSV file path - use the file in the workspace
    csv_path = "hv_aggregated_od_matrix_total_RN4_Est_Ouest.csv"

    # Output directory
    output_dir = "output_flow_charts"
    os.makedirs(output_dir, exist_ok=True)

    # Read CSV data
    print(f"Reading CSV data from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows of flow data")

    # Process CSV data into 4-level structure
    flow_data = process_csv_to_4_level_flows(df)
    print(f"Generated {len(flow_data)} 4-level flow records")

    # Extract unique categories from the processed flow data
    all_categories = set()
    for level1, level2, level3, level4, count in flow_data:
        all_categories.update([level1, level2, level3, level4])

    # Expand to individual flow tuples
    flows_4_level = []
    for level1, level2, level3, level4, count in flow_data:
        flows_4_level.extend([(level1, level2, level3, level4)] * count)

    # Create categories order from the data
    strasbourg_communes = sorted(
        [
            cat
            for cat in all_categories
            if cat.startswith("Strasbourg") and cat != "Strasbourg"
        ]
    )
    external_zones = sorted(
        [cat for cat in all_categories if not cat.startswith("Strasbourg")]
    )

    categories_order = strasbourg_communes + ["Strasbourg"] + external_zones
    print(f"Categories found: {len(categories_order)} total")
    print(f"Strasbourg communes: {len(strasbourg_communes)}")
    print(f"External zones: {len(external_zones)}")

    # Generate dynamic colors and labels
    colors = {}
    categories_labels = {}

    # Color schemes
    strasbourg_colors = [
        "#E53935",
        "#1E88E5",
        "#43A047",
        "#9C27B0",
        "#FF5722",
        "#795548",
        "#607D8B",
    ]
    external_colors = [
        "#4CAF50",
        "#2196F3",
        "#FF9800",
        "#9C27B0",
        "#00BCD4",
        "#8BC34A",
        "#FFC107",
    ]

    # Assign colors and labels for Strasbourg communes
    for i, commune in enumerate(strasbourg_communes):
        colors[commune] = strasbourg_colors[i % len(strasbourg_colors)]
        # Simplify long Strasbourg commune names
        if " - " in commune:
            categories_labels[commune] = commune.split(" - ", 1)[1]
        else:
            categories_labels[commune] = commune.replace("Strasbourg ", "")

    # Strasbourg department
    colors["Strasbourg"] = "#FF9800"
    categories_labels["Strasbourg"] = "Strasbourg"

    # External zones
    for i, zone in enumerate(external_zones):
        colors[zone] = external_colors[i % len(external_colors)]
        categories_labels[zone] = zone

    # Level titles explaining the complete aggregation/disaggregation pattern
    level_titles = [
        "Origine Strasbourg DTIR",  # Level 1: Only Strasbourg communes
        "Origines D30 - D10",  # Level 2: Strasbourg + External departments
        "Destinations D30 - D10",  # Level 3: Strasbourg + External departments
        "Destinations Strasbourg DTIR",  # Level 4: Only Strasbourg communes
    ]

    # Create FlowChartGrapher with enhanced customization for better readability
    grapher = FlowChartGrapher[str](
        output_dir,
        main_title_font_size_px=28,  # Smaller main title for complex charts
        title_font_size_px=22,  # Smaller level titles
        line_header_font_size_px=14,  # Smaller category labels
        number_font_size_px=14,  # Smaller numbers for better fit
        column_width=130,  # Slightly wider columns for better text fitting
        columns_height=600,  # Increased height for complex labels
        categories_padding=50,  # More padding for category labels to prevent cutoff
        flow_width=200,  # Wider flows for better visual impact
    )

    # Generate the aggregation/disaggregation flow chart
    output_file = grapher.make_multilevel_chart(
        file_name="strasbourg_aggregation_disaggregation",
        title="Flux OD PL Strasbourg RN4 Est-Ouest",
        flows=flows_4_level,
        level_titles=level_titles,
        colors=colors,
        categories_order=categories_order,
        categories_labels=categories_labels,
        categories_icons={},  # No icons to avoid default "car" icons
        level_spacing=400,  # Wider spacing for complex labels
    )

    print(f"Aggregation/Disaggregation flow chart generated: {output_file}")
    print("\nEnhanced Features Applied:")
    print("- Customized layout: Wider columns and flows for better readability")
    print("- Enhanced colors: Better contrast and professional color scheme")
    print("- Optimized fonts: Smaller sizes for complex multi-level visualization")
    print("\nComplete Flow Pattern Explanation:")
    print("Level 1: ONLY Strasbourg communes (all flows start at commune level)")
    print("Level 2: ALL departments (Strasbourg + external zones)")
    print("Level 3: ALL departments (complete mobility picture)")
    print("Level 4: ONLY Strasbourg communes (all flows end at commune level)")
    print("This shows complete mobility with Strasbourg commune-level detail")

    return output_file

"""
if __name__ == "__main__":
    create_strasbourg_aggregation_flow()
