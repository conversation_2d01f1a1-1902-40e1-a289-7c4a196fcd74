import logging
from typing import Dict, Generic, TypeVar

import requests

from api_abstraction.api.api import ApiFail, ApiTimeout
from mobility.ir.transport import TransportMode

inputT = TypeVar("inputT", contravariant=True)
outputT = TypeVar("outputT", covariant=True)


class BaseHereAPI(Generic[inputT, outputT]):
    display_name = "HERE Base API"
    root_url = ""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.logger = logging.getLogger("api")

        if self.root_url == "":
            raise ApiFail(f"{self.display_name} url is not specified")

    def _make_request(self, url: str, params: Dict) -> Dict:
        params["apikey"] = self.api_key

        try:
            response = requests.get(url, params=params)

            if response.status_code != 200:
                raise ApiFail(
                    (
                        f"{self.display_name} request failed "
                        f"with status {response.status_code}: {response.text}"
                    )
                )

            return response.json()
        except requests.Timeout as e:
            raise ApiTimeout(f"{self.display_name} request timed out: {str(e)}")
        except requests.RequestException as e:
            raise ApiFail(f"{self.display_name} request failed: {str(e)}")
        except ValueError as e:
            raise ApiFail(f"Failed to parse {self.display_name} response: {str(e)}")

    def _format_input_parameters(self, parameters: inputT) -> Dict:
        raise NotImplementedError()

    def _format_result(self, result: Dict) -> outputT:
        raise NotImplementedError()

    def _map_here_mode(self, mode: TransportMode) -> str:
        if mode == TransportMode.PUBLIC_TRANSPORT:
            raise ApiFail("Public transport routing has its own API. No mode needed.")
        mode_mapping = {
            TransportMode.CAR: "car",
            TransportMode.ELECTRIC_CAR: "car",
            TransportMode.BICYCLE: "bicycle",
            TransportMode.WALK: "pedestrian",
            TransportMode.MOTORCYCLE: "scooter",
            TransportMode.CARPOOLING: "car",
        }
        if mode not in mode_mapping:
            raise ApiFail(f"Mode {mode.value} unavailable in mapping")
        return mode_mapping[mode]
