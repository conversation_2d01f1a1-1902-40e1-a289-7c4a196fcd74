from typing import Optional

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.fallback_travel_time_api import FallbackTravelTimeApi
from api_abstraction.api.fastest_choice_travel_time_api import (
    FastestChoiceTravelTimeApi,
)
from api_abstraction.api.geocode_api import GeocodeApi
from api_abstraction.api.modal_travel_time_api import ModalTravelTimeApi
from api_abstraction.api.travel_time_api import TravelTimeApi
from api_abstraction.beautiful_soup.api import (
    BeautifulSoupTravelTimeAPI,
    nmp_ihm_canaltp_soup_journey_parser,
    nmp_ihm_canaltp_url_maker,
    oura_soup_journey_parser,
    oura_soup_post_data_maker,
    oura_soup_url_maker,
)
from api_abstraction.flight.flight_travel_time import FlightComputerApi
from api_abstraction.geopy.arcgis import ArcGISGeocoder
from api_abstraction.geopy.ban_france import BanFranceGeocoder
from api_abstraction.geopy.here import HereGeocoder
from api_abstraction.google.api import GoogleTravelTimeAPI
from api_abstraction.here.api import HereTravelTimeAPI
from api_abstraction.internal.travel_time import InternalTravelTimer
from api_abstraction.lux_geocode.lux_geocode import LuxGeocoder
from api_abstraction.manual.manual_geocoder import ManualGeocoder
from api_abstraction.navitia.api import NavitiaTravelTimeAPI
from api_abstraction.otp.api import OTPTravelTimeAPI
from api_abstraction.trivial.trivial_geocoder import TrivialGeocoder
from api_abstraction.trivial.trivial_travel_time import (
    DistanceTravelTimer,
    FailerTravelTimer,
    TrivialTravelTimer,
)
from mobility.ir.transport import TransportMode


class TravelTimeApiMaker:
    INTERNAL_API = None

    def __init__(
        self,
        navitia_token: Optional[str],
        google_token: Optional[str],
        reporter: EventReporter,
        airports_csv: Optional[str] = None,
        here_token: Optional[str] = None,
    ) -> None:
        self.navitia_token = navitia_token
        self.google_token = google_token
        self.here_token = here_token
        self.reporter = reporter
        self.airports_csv = airports_csv

    def get_internal_api(self) -> TravelTimeApi:
        if TravelTimeApiMaker.INTERNAL_API is None:
            TravelTimeApiMaker.INTERNAL_API = InternalTravelTimer("", self.reporter)
        return TravelTimeApiMaker.INTERNAL_API

    def make(self, kind: str) -> TravelTimeApi:
        if kind == "trivial":
            return TrivialTravelTimer("", self.reporter)
        if kind == "distance":
            return DistanceTravelTimer("", self.reporter)
        elif kind == "otp":
            return OTPTravelTimeAPI("", self.reporter)
        elif kind == "failer":
            return FailerTravelTimer("", self.reporter)
        elif kind == "google":
            if self.google_token == "" or self.google_token is None:
                raise ValueError("Google travel timer requires google key")
            otp_api = OTPTravelTimeAPI("", self.reporter)
            google_api = GoogleTravelTimeAPI(self.google_token, self.reporter)
            return ModalTravelTimeApi(
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: google_api,
                    TransportMode.CAR: google_api,
                },
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: otp_api,
                    TransportMode.CAR: otp_api,
                },
            )
        elif kind == "here":
            if self.here_token is None:
                raise ValueError("Here travel timer requires here key")
            return HereTravelTimeAPI(self.here_token, self.reporter)
        elif kind == "default":
            if (
                self.here_token == ""
                or self.google_token == ""
                or self.here_token is None
                or self.google_token is None
            ):
                raise ValueError("default travel timer requires google and here key")
            here_api = HereTravelTimeAPI(self.here_token, self.reporter)
            google_api = GoogleTravelTimeAPI(self.google_token, self.reporter)
            otp_api = OTPTravelTimeAPI("", self.reporter)
            return ModalTravelTimeApi(
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: FastestChoiceTravelTimeApi(
                        [
                            here_api,
                            google_api,
                            otp_api,
                        ]
                    ),
                    TransportMode.CAR: google_api,
                    TransportMode.CARPOOLING: google_api,
                    TransportMode.ELECTRIC_BICYCLE: otp_api,
                    TransportMode.ELECTRIC_CAR: google_api,
                    TransportMode.MOTORCYCLE: google_api,
                    TransportMode.ELECTRIC_MOTORCYCLE: google_api,
                    TransportMode.FAST_BICYCLE: otp_api,
                    TransportMode.CAR_PUBLIC_TRANSPORT: here_api,
                    TransportMode.BICYCLE_PUBLIC_TRANSPORT: here_api,
                },
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: otp_api,
                    TransportMode.BICYCLE: here_api,
                    TransportMode.ELECTRIC_BICYCLE: otp_api,
                    TransportMode.FAST_BICYCLE: otp_api,
                    TransportMode.CAR: here_api,
                },
            )
        elif kind == "nmp_ihm_canaltp":
            if self.navitia_token == "" or self.navitia_token is None:
                raise ValueError("travel timer requires navitia key")
            navitia_api = NavitiaTravelTimeAPI(self.navitia_token, self.reporter)
            nmp_ihm_bs_api = BeautifulSoupTravelTimeAPI(
                self.reporter,
                nmp_ihm_canaltp_url_maker,
                nmp_ihm_canaltp_soup_journey_parser,
            )
            return ModalTravelTimeApi(
                {
                    TransportMode.PUBLIC_TRANSPORT: nmp_ihm_bs_api,
                },
                {},
            )
        elif kind == "fastest_of_all":
            if (
                self.navitia_token == ""
                or self.google_token == ""
                or self.navitia_token is None
                or self.google_token is None
            ):
                raise ValueError(
                    "fastest_of_all travel timer requires google and navitia key"
                )
            navitia_api = NavitiaTravelTimeAPI(self.navitia_token, self.reporter)
            google_api = GoogleTravelTimeAPI(self.google_token, self.reporter)
            oura_soup_api = BeautifulSoupTravelTimeAPI(
                self.reporter,
                oura_soup_url_maker,
                oura_soup_journey_parser,
                oura_soup_post_data_maker,
            )
            otp_api = OTPTravelTimeAPI("", self.reporter)
            return ModalTravelTimeApi(
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: FastestChoiceTravelTimeApi(
                        [
                            FallbackTravelTimeApi([navitia_api, google_api]),
                            oura_soup_api,
                            otp_api,
                        ]
                    ),
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.CAR: google_api,
                },
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: otp_api,
                    TransportMode.CAR: otp_api,
                },
            )
        elif kind == "internal":
            distance_api = DistanceTravelTimer("", self.reporter)
            internal_api = self.get_internal_api()
            return ModalTravelTimeApi(
                {
                    TransportMode.WALK: internal_api,
                    TransportMode.PUBLIC_TRANSPORT: internal_api,
                    TransportMode.BICYCLE: internal_api,
                    TransportMode.CAR: internal_api,
                },
                {
                    TransportMode.WALK: distance_api,
                    TransportMode.PUBLIC_TRANSPORT: distance_api,
                    TransportMode.BICYCLE: distance_api,
                    TransportMode.CAR: distance_api,
                },
            )
        elif kind == "with_flight":
            if self.google_token == "" or self.google_token is None:
                raise ValueError("with_flight travel timer requires google key")
            if self.airports_csv is None:
                raise ValueError("with_flight travel timer requires airports csv")
            google_api = GoogleTravelTimeAPI(self.google_token, self.reporter)
            otp_api = OTPTravelTimeAPI("", self.reporter)
            flight_api = FlightComputerApi(
                "", self.reporter, self.airports_csv, google_api
            )
            return ModalTravelTimeApi(
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: google_api,
                    TransportMode.CAR: google_api,
                    TransportMode.AIRPLANE: flight_api,
                },
                {
                    TransportMode.WALK: otp_api,
                    TransportMode.PUBLIC_TRANSPORT: otp_api,
                    TransportMode.BICYCLE: otp_api,
                    TransportMode.CAR: otp_api,
                },
            )
        raise ValueError(f"Invalid travel time API kind: {kind}")


class GeocodeApiMaker:
    def __init__(
        self,
        reporter: EventReporter,
        here_token: Optional[str] = None,
        arcgis_token: Optional[str] = None,
    ) -> None:
        self.here_token = here_token
        self.arcgis_token = arcgis_token
        self.reporter = reporter

    def make(self, kind: str) -> GeocodeApi:
        if kind == "trivial":
            return TrivialGeocoder("", self.reporter)
        elif kind == "lux_geocoder":
            return LuxGeocoder("", self.reporter)
        elif kind == "here_geocoder":
            if self.here_token is None:
                raise ValueError("Here geocoder requires a token")
            else:
                return HereGeocoder(self.here_token, self.reporter)
        elif kind in {"default", "ban_france"}:
            return BanFranceGeocoder("", self.reporter)
        elif kind == "arcgis_geocoder":
            if self.arcgis_token is None:
                raise ValueError("Arcgis geocoder requires a token")
            else:
                return ArcGISGeocoder(self.arcgis_token, self.reporter)
        elif kind == "manual":
            return ManualGeocoder("", self.reporter)
        raise ValueError(f"Invalid geocoder API kind: {kind}")
