from functools import cache
from typing import Dict, <PERSON><PERSON>

import pytest

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Optional, Territory
from mobility.ir.transport import TransportMode


@pytest.fixture
def fake_travel_timer():
    def fake_api_maker(
        duration_result: int = 10, distance_result: int = 10, emission_result: int = 10
    ) -> TravelTimeApi:
        class FakeApi(TravelTimeApi):
            retry_delay_in_seconds = 0
            retries = 1
            max_call_per_period = 1000
            period_in_seconds = 0.001

            def _time(
                self,
                origin: GeoCoordinates,
                destination: GeoCoordinates,
                mode: TransportMode,
                arrival_time: Tuple[int, int],
                max_transfers: Optional[int] = None,
            ) -> JourneyAttribute:
                return JourneyAttribute(
                    duration=duration_result,
                    distance=distance_result,
                    emission=emission_result,
                )

            def compute_isochrone(
                self,
                territory: Territory,
                destination: GeoCoordinates,
                transport_mode: TransportMode,
                boundary: int,
            ) -> Dict:
                return {"type": "MultiPolygon", "coordinates": [[[[4.0, 45.0]]]]}

        return FakeApi("", EventReporter())

    return fake_api_maker


@pytest.fixture
def inapt_api():
    class InaptApi(TravelTimeApi):
        @cache
        def time(
            self,
            origin: GeoCoordinates,
            destination: GeoCoordinates,
            mode: TransportMode,
            arrival_time: Tuple[int, int],
            max_transfers: Optional[int] = None,
        ) -> JourneyAttribute:
            raise ApiInapt("I can't do this... (x__x )")

    return InaptApi("", EventReporter())


@pytest.fixture
def failing_api():
    class FailingApi(TravelTimeApi):
        @cache
        def time(
            self,
            origin: GeoCoordinates,
            destination: GeoCoordinates,
            mode: TransportMode,
            arrival_time: Tuple[int, int],
            max_transfers: Optional[int] = None,
        ) -> JourneyAttribute:
            raise ApiFail("This is... impossibru (`@')")

    return FailingApi("", EventReporter())
