import geopy
import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import ApiFail, ApiTimeout
from api_abstraction.geopy.here import HereGeocoder
from mobility.ir.geo_study import Address, GeoCoordinates


@pytest.fixture
def mock_here():
    class fake_here:
        def geocode(self, address: str) -> geopy.location.Location:
            if address == "failure of the api 34984748 complete fail":
                return None
            elif address == "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg":
                return geopy.location.Location(
                    address="15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
                    point=geopy.point.Point(
                        latitude=49.62049,
                        longitude=6.14654,
                        altitude=0.0,
                    ),
                    raw={
                        "title": "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
                        "id": "here:af:streetsection:U4YvAnC0p1aT.az2BQAn7A:CggIBCCK1ZrpAhABGgMxNUE",
                        "resultType": "houseNumber",
                        "houseNumberType": "PA",
                        "address": {
                            "label": "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
                            "countryCode": "LUX",
                            "countryName": "Luxembourg",
                            "countyCode": "LU",
                            "county": "Luxembourg",
                            "city": "Luxembourg",
                            "district": "Kirchberg",
                            "street": "Avenue John F. Kennedy",
                            "postalCode": "1855",
                            "houseNumber": "15A",
                        },
                        "position": {"lat": 49.62049, "lng": 6.14654},
                        "access": [{"lat": 49.6204, "lng": 6.14659}],
                        "mapView": {
                            "west": 6.14515,
                            "south": 49.61959,
                            "east": 6.14793,
                            "north": 49.62139,
                        },
                        "scoring": {
                            "queryScore": 0.94,
                            "fieldScore": {
                                "city": 1.0,
                                "streets": [0.78],
                                "houseNumber": 1.0,
                                "postalCode": 1.0,
                            },
                        },
                    },
                )
            else:
                raise geopy.exc.GeocoderTimedOut("timeout")

    return fake_here()


class TestHereGeocoder:
    def test_geocoder_returns_geocoordinates(self, mock_here):
        geocoder = HereGeocoder("", EventReporter())
        geocoder._geocoder = mock_here

        geoloc = geocoder.geocode(
            "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg"
        )

        assert isinstance(geoloc, GeoCoordinates)

    def test_geocoder_geocodes_an_address(self, mock_here):
        geocoder = HereGeocoder("", EventReporter())
        geocoder._geocoder = mock_here

        geoloc = geocoder.geocode(
            "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg"
        )

        assert geoloc.latitude == 49.62049 and geoloc.longitude == 6.14654

    def test_should_encode_all_the_details_from_address(self, mock_here):
        geocoder = HereGeocoder("", EventReporter())
        geocoder._geocoder = mock_here

        precise_address = geocoder.geocode_details(
            "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg"
        )

        assert precise_address == Address(
            full="15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
            normalized="15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
            city="Luxembourg",
            postcode="1855",
            citycode="1855",
            coordinates=GeoCoordinates(latitude=49.62049, longitude=6.14654),
        )

    def test_geocoder_timeouts_with_api_timeout(self, mock_here):
        geocoder = HereGeocoder("", EventReporter())
        geocoder._geocoder = mock_here
        geocoder.retry_delay_in_seconds = 0

        with pytest.raises(ApiTimeout) as e:
            geocoder.geocode("something something")
        assert e.value.message == "timeout"

    def test_geocoder_fails_on_undefined_address(self, mock_here):
        geocoder = HereGeocoder("", EventReporter())
        geocoder._geocoder = mock_here

        with pytest.raises(ApiFail) as e:
            geocoder.geocode("failure of the api 34984748 complete fail")
        assert (
            e.value.message == "Cannot find geolocalization of failure of the "
            "api 34984748 complete fail"
        )
