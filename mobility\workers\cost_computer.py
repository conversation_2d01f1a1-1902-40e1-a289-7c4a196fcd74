from collections import defaultdict
from typing import Dict, List, Union

from mobility.funky import ImmutableDict
from mobility.ir.commute_data import ModalCommuteData
from mobility.ir.cost import Cost, Cost<PERSON><PERSON>, CostPayer, Costs, IndividualCosts
from mobility.ir.employee import GeoEmployee
from mobility.ir.geo_study import Address
from mobility.ir.site import CoworkingSite, GeoSite
from mobility.ir.study import ConsolidatedCommute
from mobility.ir.study_types import Commute
from mobility.ir.transport import TransportMode
from mobility.quantity import (
    Quantity,
    adimensional,
    euros,
    gramEC,
    meters,
    remote_trip,
    remote_trip_saved,
    seconds,
    trip,
    yearly,
)
from mobility.workers.employee_cost_calculator import EmployeeCostCalculator
from mobility.workers.health_cost_calculator import HealthCostCalculator
from mobility.workers.infrastructure_cost_calculator import InfrastructureCostCalculator
from mobility.workers.transport_cost_calculator import TransportCostCalculator
from mobility.workers.work_accident_cost_calculator import WorkAccidentCostCalculator


class CostComputer:
    def __init__(self) -> None:
        self.transport = TransportCostCalculator()
        self.accidents = WorkAccidentCostCalculator()
        self.infrastructure = InfrastructureCostCalculator()
        self.employee = EmployeeCostCalculator()
        self.health = HealthCostCalculator()

    def compute_commutes_costs(
        self,
        commutes: List[ConsolidatedCommute],
        has_mobility_benefits: bool = False,
    ) -> IndividualCosts:
        return IndividualCosts(
            ImmutableDict(
                {
                    commute.origin: self.compute_commute_cost(
                        commute, has_mobility_benefits
                    )
                    for commute in commutes
                }
            )
        )

    def compute_commute_cost(
        self,
        commute: Commute[GeoEmployee, Union[GeoSite, CoworkingSite], ModalCommuteData],
        has_mobility_benefits: bool = False,
    ) -> Costs:
        if commute.data.best_mode in [
            TransportMode.CAR,
            TransportMode.ELECTRIC_CAR,
            TransportMode.MOTORCYCLE,
            TransportMode.CARPOOLING,
            TransportMode.ELECTRIC_MOTORCYCLE,
        ]:
            costs = self._compute_car_like_costs(commute)
        elif commute.data.best_mode == TransportMode.PUBLIC_TRANSPORT:
            costs = self._compute_pt_like_costs(commute)
        elif commute.data.best_mode == TransportMode.WALK:
            costs = self._compute_walk_costs(commute)
        elif commute.data.best_mode in [
            TransportMode.BICYCLE,
            TransportMode.ELECTRIC_BICYCLE,
            TransportMode.FAST_BICYCLE,
        ]:
            costs = self._compute_bicycle_like_costs(commute)
        elif commute.data.best_mode == TransportMode.CAR_PUBLIC_TRANSPORT:
            car_costs = self._compute_car_like_costs(commute)
            pt_costs = self._compute_pt_like_costs(commute)
            costs = [c * 0.8 for c in pt_costs] + [c * 0.2 for c in car_costs]
        elif commute.data.best_mode == TransportMode.BICYCLE_PUBLIC_TRANSPORT:
            pt_costs = self._compute_pt_like_costs(commute)
            bike_costs = self._compute_bicycle_like_costs(commute)
            costs = [c * 0.8 for c in pt_costs] + [c * 0.2 for c in bike_costs]
        if has_mobility_benefits:
            costs += self.employee.compute_mobility_plan_benefits()
        return tuple(costs)

    def _compute_car_like_costs(
        self,
        commute: Commute[GeoEmployee, Union[GeoSite, CoworkingSite], ModalCommuteData],
    ) -> List[Cost]:
        duration = commute.data.duration[commute.data.best_mode] * seconds
        distance = commute.data.distance[commute.data.best_mode] * meters
        emission = commute.data.emission[commute.data.best_mode] * gramEC
        destination = commute.destination
        mode_costs = self._compute_car_costs(duration, distance, emission, destination)
        if commute.data.best_mode == TransportMode.CARPOOLING:
            mode_costs = [c * (0.5 * adimensional) for c in mode_costs]
        return mode_costs

    def _compute_bicycle_like_costs(
        self,
        commute: Commute[GeoEmployee, Union[GeoSite, CoworkingSite], ModalCommuteData],
    ) -> List[Cost]:
        duration = commute.data.duration[commute.data.best_mode] * seconds
        distance = commute.data.distance[commute.data.best_mode] * meters
        destination = commute.destination
        return self._compute_bicycle_costs(duration, distance, destination)

    def _compute_car_costs(
        self,
        duration: Quantity,
        distance: Quantity,
        emission: Quantity,
        destination: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        return (
            self.infrastructure.compute_destination_cost(destination)
            + self._compute_common_employee_costs(duration)
            + self._compute_car_ride_costs(distance, emission, destination)
            + self.accidents.compute_car_accidents_costs(distance)
            + self.health.compute_health_cost_for_society(TransportMode.CAR, distance)
        )

    def _compute_pt_like_costs(
        self,
        commute: Commute[GeoEmployee, Union[GeoSite, CoworkingSite], ModalCommuteData],
    ) -> List[Cost]:
        duration = commute.data.duration[commute.data.best_mode] * seconds
        distance = commute.data.distance[commute.data.best_mode] * meters
        emission = commute.data.emission[commute.data.best_mode] * gramEC
        origin = commute.origin
        destination = commute.destination
        return self._compute_pt_costs(duration, distance, emission, origin, destination)

    def _compute_pt_costs(
        self,
        duration: Quantity,
        distance: Quantity,
        emission: Quantity,
        origin: GeoEmployee,
        destination: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        origin_address = origin.address_details
        if isinstance(destination, GeoSite):
            destination_address = destination.address_details
        else:
            destination_address = destination.address
        return (
            self.infrastructure.compute_destination_cost(destination)
            + self._compute_common_employee_costs(duration)
            + self._compute_pt_ride_costs(
                distance, emission, origin_address, destination_address
            )
            + self.health.compute_health_cost_for_society(
                TransportMode.PUBLIC_TRANSPORT, distance
            )
        )

    def _compute_bicycle_costs(
        self,
        duration: Quantity,
        distance: Quantity,
        destination: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        return (
            self.infrastructure.compute_destination_cost(destination)
            + self._compute_common_employee_costs(duration)
            + self._compute_bicycle_ride_costs(distance, destination)
            + self.health.compute_health_cost_for_society(
                TransportMode.BICYCLE, distance
            )
        )

    def _compute_walk_costs(
        self,
        commute: Commute[GeoEmployee, Union[GeoSite, CoworkingSite], ModalCommuteData],
    ) -> List[Cost]:
        duration = commute.data.duration[TransportMode.WALK] * seconds
        distance = commute.data.distance[TransportMode.WALK] * meters
        return (
            self.infrastructure.compute_destination_cost(commute.destination)
            + self._compute_common_employee_costs(duration)
            + self._compute_walk_ride_costs(distance)
            + self.health.compute_health_cost_for_society(TransportMode.WALK, distance)
        )

    def _compute_car_ride_costs(
        self,
        distance: Quantity,
        emission: Quantity,
        destination: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        costs = [
            Cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=self.transport.compute_car_km_fee(distance),
            ),
            Cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_car_noise_damage_cost(distance),
            ),
            Cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_car_pollution_damage_cost(distance),
            ),
            Cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_car_congestion_damage_cost(distance),
            ),
            Cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_carbone_cost(emission),
            ),
        ]
        costs.extend(self._compute_car_parking_commute_costs(destination))
        return costs

    def _compute_walk_ride_costs(self, distance: Quantity) -> List[Cost]:
        return [
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=self.accidents.compute_walk_work_accident_cost_for_company(
                    distance
                ),
            ),
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=self.accidents.compute_walk_work_accident_cost_for_society(
                    distance
                ),
            ),
            Cost(
                kind=CostKind.WALK_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=self.transport.compute_walk_km_fee(distance),
            ),
        ]

    def _compute_bicycle_ride_costs(
        self, distance: Quantity, destination: Union[GeoSite, CoworkingSite]
    ) -> List[Cost]:
        costs = [
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=self.accidents.compute_bicycle_work_accident_cost_for_company(
                    distance
                ),
            ),
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=self.accidents.compute_bicycle_work_accident_cost_for_society(
                    distance
                ),
            ),
            Cost(
                kind=CostKind.BIKE_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=self.transport.compute_bicycle_km_fee(distance),
            ),
            Cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=self.employee.compute_sustainable_mobility_fee_for_employee(),
            ),
            Cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.COMPANY,
                amount=self.employee.compute_sustainable_mobility_fee_for_company(),
            ),
        ]
        costs.extend(self._compute_bicycle_parking_commute_costs(destination))
        return costs

    def _compute_pt_ride_costs(
        self,
        distance: Quantity,
        emission: Quantity,
        origin: Address,
        destination: Address,
    ) -> List[Cost]:
        return [
            Cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_pt_noise_damage_cost(distance),
            ),
            Cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_pt_pollution_damage_cost(distance),
            ),
            Cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=self.transport.compute_carbone_cost(emission),
            ),
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=self.accidents.compute_pt_work_accident_cost_for_company(
                    distance
                ),
            ),
            Cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=self.accidents.compute_pt_work_accident_cost_for_society(
                    distance
                ),
            ),
            Cost(
                kind=CostKind.PT_SUBSCRIPTION,
                payer=CostPayer.COMPANY,
                amount=self.transport.compute_pt_subscription_cost_for_company(
                    origin, destination, distance
                ),
            ),
            Cost(
                kind=CostKind.PT_SUBSCRIPTION,
                payer=CostPayer.EMPLOYEE,
                amount=self.transport.compute_pt_subscription_cost_for_employee(
                    origin, destination, distance
                ),
            ),
        ]

    def _compute_car_parking_commute_costs(
        self,
        site: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        if isinstance(site, CoworkingSite):
            return []

        company_base_infrastructure_cost = (
            self.infrastructure.compute_car_parking_infrastructure_cost()
        )
        costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=company_base_infrastructure_cost,
            )
        ]
        if isinstance(site, GeoSite) and site.car_parking_cost is not None:
            employee_fee = site.car_parking_cost * euros / trip
            costs += [
                Cost(
                    kind=CostKind.CAR_PARKING,
                    payer=CostPayer.EMPLOYEE,
                    amount=employee_fee,
                ),
                Cost(
                    kind=CostKind.CAR_PARKING,
                    payer=CostPayer.COMPANY,
                    amount=-employee_fee,
                ),
            ]

        return costs

    def _compute_bicycle_parking_commute_costs(
        self,
        site: Union[GeoSite, CoworkingSite],
    ) -> List[Cost]:
        if isinstance(site, CoworkingSite):
            return []

        company_base_infrastructure_cost = (
            self.infrastructure.compute_bicycle_parking_infrastructure_cost()
        )
        costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=company_base_infrastructure_cost,
            )
        ]
        if isinstance(site, GeoSite) and site.bicycle_parking_cost is not None:
            employee_fee = site.bicycle_parking_cost * euros / trip
            costs += [
                Cost(
                    kind=CostKind.BIKE_PARKING,
                    payer=CostPayer.EMPLOYEE,
                    amount=employee_fee,
                ),
                Cost(
                    kind=CostKind.BIKE_PARKING,
                    payer=CostPayer.COMPANY,
                    amount=-employee_fee,
                ),
            ]

        return costs

    def _compute_common_employee_costs(self, duration: Quantity) -> List[Cost]:
        return [
            Cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=self.transport.compute_travel_time_cost(duration),
            ),
        ]

    def compute_cost_for_carpooling_employee(
        self,
        group_commute: ConsolidatedCommute,
        employee_commute: ConsolidatedCommute,
        ride_share_factor: float,
    ) -> Costs:
        distance = group_commute.data.distance[TransportMode.CAR] * meters
        emission = group_commute.data.emission[TransportMode.CAR] * gramEC
        adjusted_car_ride_costs = [
            Cost(
                kind=cost.kind, payer=cost.payer, amount=cost.amount * ride_share_factor
            )
            for cost in self._compute_car_ride_costs(
                distance, emission, group_commute.destination
            )
        ]
        original_distance = employee_commute.data.distance[TransportMode.CAR] * meters
        original_duration = employee_commute.data.duration[TransportMode.CAR] * seconds
        return tuple(
            self._compute_common_employee_costs(original_duration)
            + self.infrastructure.compute_destination_cost(group_commute.destination)
            + adjusted_car_ride_costs
            + self.health.compute_health_cost_for_society(
                TransportMode.CAR, original_distance
            )
            + self.accidents.compute_car_accidents_costs(original_distance)
            + self.employee.compute_sustainability_fee()
            + self.employee.compute_mobility_plan_benefits()
        )

    def compute_remote_commutes_costs(
        self,
        commutes: List[ConsolidatedCommute],
    ) -> IndividualCosts:
        commutes_by_site_by_mode: Dict[
            GeoSite, Dict[TransportMode, List[ConsolidatedCommute]]
        ] = defaultdict(lambda: defaultdict(list))
        for commute in commutes:
            commutes_by_site_by_mode[commute.destination][
                commute.data.best_mode
            ].append(commute)
        cost_by_employee = {}
        for site, commutes_by_mode in commutes_by_site_by_mode.items():
            e_costs = self._compute_remote_commutes_cost_on_single_site(
                site, commutes_by_mode
            )
            for employee, costs in e_costs.items():
                cost_by_employee[employee] = costs
        return IndividualCosts(ImmutableDict(cost_by_employee))

    def _compute_remote_commutes_cost_on_single_site(
        self,
        site: GeoSite,
        commutes_by_mode: Dict[TransportMode, List[ConsolidatedCommute]],
    ) -> Dict[GeoEmployee, Costs]:
        single_employee_saving_costs = [
            CostKind.CAR_KM_FEE,
            CostKind.WALK_KM_FEE,
            CostKind.BIKE_KM_FEE,
            CostKind.NOISE_DAMAGE,
            CostKind.POLLUTION_DAMAGE,
            CostKind.INDUCED_CONGESTION,
            CostKind.CARBON_IMPACT,
            CostKind.TRAVEL_TIME,
            CostKind.WORK_ACCIDENTS,
            CostKind.HEALTH_IMPACT,
        ]
        single_employee_trip_saved_factor = (
            compute_remote_employee_trip_savings_factor()
        )
        nb_remote_workers_at_site = sum(
            len(commutes) for commutes in commutes_by_mode.values()
        )
        site_spots_saving_factor = compute_remote_savings_from_saved_spots(
            nb_remote_workers_at_site
        )
        cost_by_employee = {}
        for mode, commutes in commutes_by_mode.items():
            nb_remote_workers_with_same_mode = len(commutes)
            mode_spots_saving_factor = compute_remote_savings_from_saved_spots(
                nb_remote_workers_with_same_mode
            )
            for commute in commutes:
                base_costs = self.compute_commute_cost(
                    commute, has_mobility_benefits=True
                )
                remote_costs = []
                for cost in base_costs:
                    if cost.kind in single_employee_saving_costs:
                        remote_cost = cost * single_employee_trip_saved_factor
                    elif cost.kind in [CostKind.OFFICE_RENT, CostKind.COWORKING_RENT]:
                        remote_cost = cost * site_spots_saving_factor
                    elif (
                        cost.kind == CostKind.CAR_PARKING and mode == TransportMode.CAR
                    ):
                        remote_cost = cost * mode_spots_saving_factor
                    elif (
                        cost.kind == CostKind.BIKE_PARKING
                        and mode == TransportMode.BICYCLE
                    ):
                        remote_cost = cost * mode_spots_saving_factor
                    else:
                        remote_cost = cost
                    remote_costs.append(remote_cost)
                remote_costs.append(
                    Cost(
                        payer=CostPayer.COMPANY,
                        kind=CostKind.HOME_OFFICE,
                        amount=600 * euros / yearly,
                    )
                )
                cost_by_employee[commute.origin] = tuple(remote_costs)
        return cost_by_employee

    def compute_environment_damage_cost_for_car(
        self, distance: Quantity, emission: Quantity
    ) -> Quantity:
        return (
            self.transport.compute_car_pollution_damage_cost(distance)
            + self.transport.compute_carbone_cost(emission)
            + self.transport.compute_car_noise_damage_cost(distance)
        )

    def compute_environment_damage_cost_for_pt(
        self, distance: Quantity, emission: Quantity
    ) -> Quantity:
        return (
            self.transport.compute_pt_pollution_damage_cost(distance)
            + self.transport.compute_carbone_cost(emission)
            + self.transport.compute_pt_noise_damage_cost(distance)
        )


def compute_remote_employee_trip_savings_factor() -> Quantity:
    return trip / remote_trip


def compute_number_of_remote_workers_to_save_a_spot() -> Quantity:
    return remote_trip_saved / trip


def compute_remote_savings_from_saved_spots(nb_remote_workers: int) -> Quantity:
    nb_workers_per_spot = compute_number_of_remote_workers_to_save_a_spot()
    nb_spots_saved = (nb_remote_workers * adimensional) // nb_workers_per_spot
    return 1 * adimensional - nb_spots_saved / nb_remote_workers
