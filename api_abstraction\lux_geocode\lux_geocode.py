from typing import Any, Dict

import requests

from api_abstraction.api.api import ApiFail
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import GeocodeApi
from mobility.ir.geo_study import Address, GeoCoordinates


class LuxGeocoder(GeocodeApi):
    def __init__(self, token: str, reporter: EventReporter):
        super().__init__(token, reporter)
        self.lux_api_url = "http://apiv3.geoportail.lu/geocode/search"
        self.call_request = self._call_request

    def _geocode(self, address: str) -> GeoCoordinates:
        request_json_result = self.call_request(
            self.lux_api_url, {"queryString": address}
        )
        try:
            coordinates = request_json_result["results"][0]["geomlonlat"]["coordinates"]
        except KeyError:
            raise ApiFail(f"No geocoordinates found in result {request_json_result}")
        return GeoCoordinates(latitude=coordinates[1], longitude=coordinates[0])

    def _call_request(self, url: str, args: Dict[str, Any]) -> Dict:
        return requests.get(url, params=args).json()

    # def _reverse(self, coords: GeoCoordinates) -> Address:
    #     coordinates = GeoCoordinates(
    #         latitude=coords.latitude, longitude=coords.longitude
    #     )
    #     return dataclasses.replace(self.address, coordinates=coordinates)

    # def _bulk_reverse(self, coords: List[GeoCoordinates]) -> List[Address]:
    #     return [self._reverse(c) for c in coords]

    def _geocode_details(self, address: str) -> Address:
        request_json_result = self.call_request(
            self.lux_api_url, {"queryString": address}
        )
        try:
            result = request_json_result["results"][0]
            coordinates = result["geomlonlat"]["coordinates"]
            normalized_address = result["address"]
            city = result["AddressDetails"]["locality"]
            postcode = result["AddressDetails"]["zip"]
        except KeyError:
            raise ApiFail(f"Info missing in result {request_json_result}")
        if postcode is None:
            raise ApiFail(f"No zip code returned in {request_json_result}")
        return Address(
            full=address,
            normalized=normalized_address,
            city=city,
            postcode=postcode,
            citycode=postcode,
            coordinates=GeoCoordinates(
                latitude=coordinates[1], longitude=coordinates[0]
            ),
        )
