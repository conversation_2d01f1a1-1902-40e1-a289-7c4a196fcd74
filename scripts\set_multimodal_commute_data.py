"""
This script modifies a json consolidated study with commute data
given from a json file referencing base mode data for each
employee to update. The base mode commute data is added to make
the final commute data:
{
    "employee_nickname": {
        "base_mode": {
            "duration": 1200,  # s, required
            "distance": 11000,  # km, required
            "emission": 250,  # gCO2eq, optional
        }
    }
}
Only works for studies with one scenario, not on alternative arrival time.
"""

import argparse
import dataclasses
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from api_abstraction.api.travel_time_api import JourneyAttribute
from mobility.builders.json_builder import StudyJsonBuilder
from mobility.funky import ImmutableDict
from mobility.ir.commute_data import ModalCommuteData
from mobility.ir.scenario_data import ScenarioData
from mobility.ir.study import ConsolidatedCommute, ConsolidatedStudy
from mobility.ir.transport import TransportMode
from mobility.quantity import gramEC, meters, seconds
from mobility.serializers.json_serializer import StudyJsonSerializer
from mobility.workers.emission_computer import compute_mode_emission_from_distance

RawCommuteData = Dict[str, Dict[str, Dict[str, int]]]
MultiModalCommuteData = Dict[str, Dict[TransportMode, JourneyAttribute]]


def parse_args(
    args: Optional[List[str]] = None,
) -> Tuple[ConsolidatedStudy, RawCommuteData, str]:
    parser = argparse.ArgumentParser()
    parser.add_argument("study_file", help="Consolidated study json file")
    parser.add_argument(
        "multimodal_commutes",
        help="Json file containing multimodal commute data for each employee to update",
    )
    parsed_args = parser.parse_args(args)
    study = StudyJsonBuilder(parsed_args.study_file).build_consolidated_study()
    with open(parsed_args.multimodal_commutes) as f:
        raw_data = json.load(f)
    workdir = os.path.dirname(parsed_args.study_file)
    return study, raw_data, workdir


def parse_raw_commute_data(raw_commute_data: RawCommuteData) -> MultiModalCommuteData:
    employees_multimodal_commutes = {}
    for employee_nickname, raw_modal_commute_data in raw_commute_data.items():
        modal_commute_data = {}
        for raw_mode, journey in raw_modal_commute_data.items():
            mode = TransportMode.from_string(raw_mode)
            modal_commute_data[mode] = JourneyAttribute(
                duration=int(journey["duration"]),
                distance=int(journey["distance"]),
                emission=(
                    None if "emission" not in journey else int(journey["emission"])
                ),
            )
        employees_multimodal_commutes[employee_nickname] = modal_commute_data
    return employees_multimodal_commutes


def update_study(
    study: ConsolidatedStudy,
    multimodal_commute_data: MultiModalCommuteData,
) -> ConsolidatedStudy:
    mode_to_update = TransportMode.PUBLIC_TRANSPORT

    def updater(
        commute: ConsolidatedCommute, scenario_data: ScenarioData
    ) -> ModalCommuteData:
        employee = commute.origin.nickname
        if employee in multimodal_commute_data:
            new_duration = 0
            new_distance = 0
            new_emission = 0
            for mode, journey in multimodal_commute_data[employee].items():
                new_duration += journey.duration
                if journey.distance is None:
                    raise ValueError(
                        f"None distance for employee {employee} journey by {mode}"
                    )
                new_distance += journey.distance
                if journey.emission is None:
                    delta_emission = compute_mode_emission_from_distance(
                        mode, journey.distance
                    )
                else:
                    delta_emission = journey.emission
                new_emission += delta_emission
            new_durations = dict(commute.data.duration)
            new_durations[mode_to_update] = new_duration
            new_distances = dict(commute.data.distance)
            new_distances[mode_to_update] = new_distance
            new_emissions = dict(commute.data.emission)
            new_emissions[mode_to_update] = new_emission
            new_commute_data = dataclasses.replace(
                commute.data,
                duration=ImmutableDict(new_durations),
                distance=ImmutableDict(new_distances),
                emission=ImmutableDict(new_emissions),
            )
            print(
                f"Updated commute for employee {employee}:\n"
                f"  t={new_commute_data.duration[mode_to_update] * seconds}\n"
                f"  d={new_commute_data.distance[mode_to_update] * meters}\n"
                f"  e={new_commute_data.emission[mode_to_update] * gramEC}\n"
            )
        else:
            new_commute_data = commute.data
        return new_commute_data

    updated_scenarios = study.scenarios.update_commutes_with_context(updater)
    return dataclasses.replace(study, scenarios=updated_scenarios)


def main(args: Optional[List[str]] = None) -> None:
    logging.getLogger().setLevel(logging.INFO)
    study, raw_data, workdir = parse_args(args)
    mulmodal_data = parse_raw_commute_data(raw_data)
    updated_study = update_study(study, mulmodal_data)
    report_time = datetime.today().strftime("%y%m%d-%H%M%S")
    suffix = f"multimodal_commutes_updated_{report_time}"
    StudyJsonSerializer(workdir, updated_study, suffix).serialize()


def execute_script(name: str, args: Optional[List[str]] = None) -> None:
    if name == "__main__":
        main(args)


execute_script(__name__)
