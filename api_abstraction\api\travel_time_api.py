from dataclasses import dataclass
from functools import cache
from typing import Dict, List, Optional, Tuple

from retry.api import retry_call

from api_abstraction.api.api import AbstractAPI, ApiFail, ApiTimeout
from api_abstraction.api.event_reporter import <PERSON><PERSON><PERSON>
from mobility.funky import get_some
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode


@dataclass(eq=True, frozen=True)
class JourneyAttribute:
    duration: int  # duration in seconds
    distance: Optional[int]  # distance in meters
    emission: Optional[int]  # GHG emissions in gEC

    def __add__(self, other: "JourneyAttribute") -> "JourneyAttribute":
        return JourneyAttribute(
            duration=self.duration + other.duration,
            distance=get_some(self.distance) + get_some(other.distance),
            emission=get_some(self.emission) + get_some(other.emission),
        )


class TravelTimeApi(AbstractAPI):
    @cache
    def time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        burst = self.new_burst()
        return retry_call(
            self.rate_limited_time,
            fargs=[origin, destination, mode, arrival_time, burst, max_transfers],
            exceptions=ApiTimeout,
            tries=self.retries,
            delay=self.retry_delay_in_seconds,  # type: ignore[arg-type]
        )

    def rate_limited_time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        burst: Burst,
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        """Copy this method with its RateLimiter decorator to edit max_calls"""
        with self.rate_limiter:
            transaction = burst.new_transaction()
            transaction.start(self.log_from_query(origin, destination, mode))
            try:
                result = self._time(
                    origin, destination, mode, arrival_time, max_transfers
                )
                transaction.succeed(self.log_from_result(result))
            except ApiFail as e:
                transaction.fail(e)
                raise
        return result

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        raise NotImplementedError(f"_time {self.__class__}")

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        raise NotImplementedError(f"_compute_isochrone {self.__class__}")

    def compute_detour_costs(
        self,
        origins: List[GeoCoordinates],
        destination: GeoCoordinates,
    ) -> Dict[int, Dict[int, float]]:
        raise NotImplementedError(f"_compute_detour_costs {self.__class__}")

    def compute_detours(
        self,
        origins: List[GeoCoordinates],
        destination: GeoCoordinates,
    ) -> Dict[int, Dict[int, JourneyAttribute]]:
        raise NotImplementedError(f"_compute_detour_costs {self.__class__}")

    def log_from_query(
        self, origin: GeoCoordinates, destination: GeoCoordinates, mode: TransportMode
    ) -> str:
        return f"Time query from ({origin}) to ({destination}) using {mode}"

    def log_from_result(self, result: JourneyAttribute) -> str:
        return str(result)
