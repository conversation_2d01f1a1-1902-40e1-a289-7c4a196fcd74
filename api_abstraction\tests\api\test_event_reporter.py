import datetime

from api_abstraction.api.event_reporter import <PERSON><PERSON><PERSON>, EventReporter, Transaction


class TestEventReporter:
    def test_event_reporter_gives_stored_burst(self):
        reporter = EventReporter()

        burst = reporter.new_burst("dummy_api")

        assert isinstance(burst, <PERSON><PERSON><PERSON>)
        assert len(reporter.bursts) == 1

    def test_burst_gives_stored_transaction(self):
        burst = Burst("dummy_api")

        transaction = burst.new_transaction()

        assert isinstance(transaction, Transaction)
        assert len(burst.transactions) == 1

    def test_event_reporter_reports_one_error_one_time(self):
        reporter = EventReporter()
        burst = reporter.new_burst("dummy_api")
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.fail(Exception("badaboum"))
        transaction.query_time = transaction.response_time - datetime.timedelta(
            seconds=0, milliseconds=10
        )

        assert burst.nb_failure == 1
        assert burst.nb_success == 0

    def test_strain_is_null_if_tranction_is_longer_than_strain_interval(self):
        reporter = EventReporter()
        burst = reporter.new_burst("dummy_api")
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.fail(Exception("badaboum"))
        transaction.query_time = transaction.response_time - datetime.timedelta(
            seconds=1, milliseconds=10
        )

        strain = reporter.compute_strain(transaction, burst.api_class)

        assert strain == 0.0

    def test_event_reporter_reports_one_succes_failing_two_times(self):
        reporter = EventReporter()
        burst = reporter.new_burst("dummy_api")
        first_transaction = burst.new_transaction()
        first_transaction.start("glouglou")
        first_transaction.fail(Exception("badaboum"))
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.fail(Exception("crac boum"))
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.succeed("youpie")
        first_transaction.query_time = transaction.response_time - datetime.timedelta(
            milliseconds=10
        )

        assert burst.nb_failure == 2
        assert burst.nb_success == 1

    def test_event_reporter_reports_number_of_requests_one_second_before_a_failure(
        self,
    ):
        reporter = EventReporter()
        burst = reporter.new_burst("dummy_api")
        transaction_before_last_second = burst.new_transaction()
        transaction_before_last_second.start("glagla")
        transaction_before_last_second.succeed("youpie")
        burst = reporter.new_burst("dummy_api")
        transaction_on_horse_last_second = burst.new_transaction()
        transaction_on_horse_last_second.start("gleugleu")
        transaction_on_horse_last_second.succeed("youpie")
        burst = reporter.new_burst("dummy_api")
        transaction = burst.new_transaction()
        transaction.start("gligli")
        transaction.succeed("youpie")
        burst = reporter.new_burst("dummy_api")
        transaction = burst.new_transaction()
        transaction.start("gloglo")
        transaction.succeed("youpie")
        burst = reporter.new_burst("dummy_api")
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.fail(Exception("badaboum"))
        transaction_before_last_second.query_time = (
            transaction.response_time - datetime.timedelta(seconds=1, milliseconds=3)
        )
        transaction_before_last_second.response_time = (
            transaction.response_time - datetime.timedelta(seconds=1, milliseconds=2)
        )
        transaction_on_horse_last_second.query_time = (
            transaction.response_time - datetime.timedelta(seconds=1, milliseconds=1)
        )
        transaction_on_horse_last_second.response_time = (
            transaction.response_time - datetime.timedelta(milliseconds=1)
        )
        transaction.query_time = transaction.response_time - datetime.timedelta(
            milliseconds=10
        )

        strain = reporter.compute_strain(transaction, burst.api_class)

        assert strain == 2.5

    def test_event_reporter_reports_duration_of_burst_before_success(self):
        reporter = EventReporter()
        burst = reporter.new_burst("dummy_api")
        transaction_first = burst.new_transaction()
        transaction_first.start("glouglou")
        transaction_first.fail(Exception("badaboum"))
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.fail(Exception("crac boum"))
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.succeed("youpie")
        delta_time = datetime.timedelta(seconds=2, milliseconds=324)
        transaction_first.query_time = transaction.response_time - delta_time

        strain = reporter.compute_strain(transaction, burst.api_class)

        assert strain == 1.5

    def test_api_counts(self):
        reporter = EventReporter()
        burst = reporter.new_burst("dummy_api")
        transaction_first = burst.new_transaction()
        transaction_first.start("glouglou")
        transaction_first.fail(Exception("badaboum"))
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.fail(Exception("crac boum"))
        transaction = burst.new_transaction()
        transaction.start("glouglou")
        transaction.succeed("youpie")

        assert reporter.compute_nb_call_by_api() == [("dummy_api", 1, 2)]

    def test_format_nb_call_by_api(self):
        assert EventReporter.format_nb_call_by_api([("dummy_api", 1, 2)]) == [
            "dummy_api: 1 success calls, 2 error calls"
        ]

    def test_format_burst_strain(self):
        assert (
            EventReporter.format_burst_strain(1.3, 1.5, datetime.timedelta(seconds=1))
            == "Failure when strain > 1.5 / 1.0 s success when strain < 1.3 / 1.0 s"
        )

    def test_format_burst_error(self):
        assert EventReporter.format_burst_error(3, 2) == "2 failures, 3 success"

    def test_format_burst_duration(self):
        assert (
            EventReporter.format_burst_duration(
                datetime.timedelta(seconds=1, milliseconds=3)
            )
            == "1.003 s before completion"
        )
