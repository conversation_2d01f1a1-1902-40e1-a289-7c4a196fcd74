from functools import lru_cache
from typing import List

from retry.api import retry_call

from api_abstraction.api.api import Abstract<PERSON><PERSON>, ApiFail, ApiTimeout
from api_abstraction.api.event_reporter import <PERSON><PERSON><PERSON>
from mobility.ir.geo_study import Address, GeoCoordinates


class GeocodeApi(AbstractAPI):
    @lru_cache(128)
    def geocode(self, address: str) -> GeoCoordinates:
        burst = self.new_burst()
        return retry_call(
            self.rate_limited_geocode,
            fargs=[address, burst],
            exceptions=ApiTimeout,
            tries=self.retries,
            delay=self.retry_delay_in_seconds,  # type: ignore[arg-type]
        )

    @lru_cache(128)
    def geocode_details(self, address: str) -> Address:
        burst = self.new_burst()
        return retry_call(
            self.rate_limited_geocode_details,
            fargs=[address, burst],
            exceptions=ApiTimeout,
            tries=self.retries,
            delay=self.retry_delay_in_seconds,  # type: ignore[arg-type]
        )

    @lru_cache(128)
    def reverse(self, coords: GeoCoordinates) -> Address:
        burst = self.new_burst()
        return retry_call(
            self.rate_limited_reverse,
            fargs=[coords, burst],
            exceptions=ApiTimeout,
            tries=self.retries,
            delay=self.retry_delay_in_seconds,  # type: ignore[arg-type]
        )

    def bulk_reverse(self, coords: List[GeoCoordinates]) -> List[Address]:
        burst = self.new_burst()
        return retry_call(
            self.rate_limited_bulk_reverse,
            fargs=[coords, burst],
            exceptions=ApiTimeout,
            tries=self.retries,
            delay=self.retry_delay_in_seconds,  # type: ignore[arg-type]
        )

    def rate_limited_geocode(self, address: str, burst: Burst) -> GeoCoordinates:
        with self.rate_limiter:
            transaction = burst.new_transaction()
            transaction.start(self.log_from_string(address))
            try:
                result = self._geocode(address)
                transaction.succeed(self.log_from_coordinates(result))
            except ApiFail as e:
                transaction.fail(e)
                raise
        return result

    def rate_limited_geocode_details(self, address: str, burst: Burst) -> Address:
        with self.rate_limiter:
            transaction = burst.new_transaction()
            transaction.start(self.log_from_string(address))
            try:
                result = self._geocode_details(address)
                transaction.succeed(self.log_from_address(result))
            except ApiFail as e:
                transaction.fail(e)
                raise
        return result

    def rate_limited_reverse(self, coords: GeoCoordinates, burst: Burst) -> Address:
        with self.rate_limiter:
            transaction = burst.new_transaction()
            transaction.start(self.log_from_coordinates(coords))
            try:
                result = self._reverse(coords)
                transaction.succeed(self.log_from_address(result))
            except ApiFail as e:
                transaction.fail(e)
                raise
        return result

    def rate_limited_bulk_reverse(
        self, coords: List[GeoCoordinates], burst: Burst
    ) -> List[Address]:
        with self.rate_limiter:
            transaction = burst.new_transaction()
            transaction.start(self.log_from_coordinates_list(coords))
            try:
                result = self._bulk_reverse(coords)
                transaction.succeed(self.log_from_address_list(result))
            except ApiFail as e:
                transaction.fail(e)
                raise
        return result

    def _geocode(self, address: str) -> GeoCoordinates:
        raise NotImplementedError

    def _geocode_details(self, address: str) -> Address:
        raise NotImplementedError

    def _reverse(self, coords: GeoCoordinates) -> Address:
        raise NotImplementedError

    def _bulk_reverse(self, coords: List[GeoCoordinates]) -> List[Address]:
        raise NotImplementedError

    def log_from_string(self, s: str) -> str:
        return s

    def log_from_coordinates(self, result: GeoCoordinates) -> str:
        return str(result)

    def log_from_address(self, address: Address) -> str:
        return str(address)

    def log_from_coordinates_list(self, result: List[GeoCoordinates]) -> str:
        return str(result)

    def log_from_address_list(self, address: List[Address]) -> str:
        return str(address)
