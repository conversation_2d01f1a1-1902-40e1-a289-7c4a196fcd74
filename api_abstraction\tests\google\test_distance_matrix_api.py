from datetime import datetime
from typing import Any, Dict
from unittest.mock import Mock, patch

import googlemaps
import pytest

from api_abstraction.api.api import ApiFail, ApiTimeout
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.google.base_google_api import GoogleRequestParameters
from api_abstraction.google.distance_matrix import DistanceMatrix
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


@pytest.fixture
def failing_traffic_distance_matrix() -> Any:
    class fake_distance_matrix:
        def __init__(self, n_failing_calls: int):
            self.n_calls = 0
            self.n_failing_calls = n_failing_calls

        def distance_matrix(self, *args: Any, **kwargs: Any) -> Dict:
            self.n_calls += 1
            if self.n_calls <= self.n_failing_calls:
                return {
                    "destination_addresses": ["somewhere"],
                    "origin_addresses": ["somewhere"],
                    "rows": [
                        {
                            "elements": [
                                {
                                    "distance": {"text": "55,9 km", "value": 55933},
                                    "duration": {"text": "46 minutes", "value": 2763},
                                    "status": "OK",
                                }
                            ]
                        }
                    ],
                    "status": "OK",
                }
            else:
                return {
                    "destination_addresses": ["somewhere"],
                    "origin_addresses": ["somewhere"],
                    "rows": [
                        {
                            "elements": [
                                {
                                    "distance": {"text": "55,9 km", "value": 55933},
                                    "duration": {"text": "46 minutes", "value": 2763},
                                    "duration_in_traffic": {
                                        "text": "1 heure 10 min",
                                        "value": 4212,
                                    },
                                    "status": "OK",
                                }
                            ]
                        }
                    ],
                    "status": "OK",
                }

    return fake_distance_matrix


@pytest.fixture
def mock_distance_matrix_response() -> Dict[str, Any]:
    return {
        "status": "OK",
        "rows": [
            {
                "elements": [
                    {
                        "status": "OK",
                        "duration": {"value": 2763, "text": "46 mins"},
                        "duration_in_traffic": {"value": 3600, "text": "1 hour"},
                        "distance": {"value": 55933, "text": "55.9 km"},
                    }
                ]
            }
        ],
    }


@pytest.fixture(autouse=True)
def mock_googlemaps_client() -> Any:
    with patch("api_abstraction.google.distance_matrix.googlemaps.Client") as mock:
        client = Mock()
        mock.return_value = client
        yield client


@pytest.fixture
def fake_googlemaps_exception_raiser() -> Any:
    class fake_compute_route:
        def __init__(self, exception: Exception):
            self.e = exception

        def distance_matrix(self, *args: Any, **kwargs: Any) -> None:
            raise self.e

    return fake_compute_route


class TestDistanceMatrix:
    def test_compute_route_formats_request_and_returns_journey_attributes(
        self, mock_googlemaps_client: Any, mock_distance_matrix_response: Dict[str, Any]
    ) -> None:
        api = DistanceMatrix("AIza fake key")
        mock_googlemaps_client.distance_matrix.return_value = (
            mock_distance_matrix_response
        )
        params = GoogleRequestParameters(
            origin=GeoCoordinates(46.1146, 4.7031),
            destination=GeoCoordinates(45.7668, 4.8566),
            mode=TransportMode.CAR,
            departure_time=datetime(2023, 1, 1, 8, 30),
            arrival_time=None,
        )

        result = api.compute_route(params)

        assert result == JourneyAttribute(duration=3600, distance=55933, emission=None)

    def test_formats_input_parameters_correctly(self) -> None:
        api = DistanceMatrix("AIza fake key")
        departure_time = datetime(2023, 1, 1, 8, 30)
        params = GoogleRequestParameters(
            origin=GeoCoordinates(46.1146, 4.7031),
            destination=GeoCoordinates(45.7668, 4.8566),
            mode=TransportMode.CAR,
            departure_time=departure_time,
            arrival_time=None,
        )

        result = api._format_input_parameters(params)

        assert result == {
            "origins": [(46.1146, 4.7031)],
            "destinations": [(45.7668, 4.8566)],
            "mode": "driving",
            "departure_time": departure_time,
            "arrival_time": None,
            "traffic_model": "pessimistic",
            "language": "fr",
            "units": "metric",
        }

    @pytest.mark.parametrize(
        "mode,expected_google_mode",
        [
            (TransportMode.WALK, "walking"),
            (TransportMode.PUBLIC_TRANSPORT, "transit"),
            (TransportMode.CAR, "driving"),
            (TransportMode.ELECTRIC_CAR, "driving"),
            (TransportMode.CARPOOLING, "driving"),
            (TransportMode.BICYCLE, "bicycling"),
            (TransportMode.MOTORCYCLE, "driving"),
            (TransportMode.ELECTRIC_MOTORCYCLE, "driving"),
        ],
    )
    def test_maps_transport_modes_correctly(
        self, mode: TransportMode, expected_google_mode: str
    ) -> None:
        api = DistanceMatrix("AIza fake key")

        result = api._map_google_mode(mode)

        assert result == expected_google_mode

    @pytest.mark.parametrize(
        "unsupported_mode",
        [
            TransportMode.ELECTRIC_BICYCLE,
            TransportMode.FAST_BICYCLE,
            TransportMode.CAR_PUBLIC_TRANSPORT,
            TransportMode.BICYCLE_PUBLIC_TRANSPORT,
            TransportMode.AIRPLANE,
        ],
    )
    def test_raises_error_for_unsupported_mode(
        self, unsupported_mode: TransportMode
    ) -> None:
        api = DistanceMatrix("AIza fake key")

        with pytest.raises(
            ApiFail, match=f"Mode {unsupported_mode.value} unavailable in mapping"
        ):
            api._map_google_mode(unsupported_mode)

    def test_raises_error_if_status_not_ok(self, mock_googlemaps_client: Any) -> None:
        api = DistanceMatrix("AIza fake key")
        error_response = {"status": "INVALID_REQUEST", "rows": []}
        mock_googlemaps_client.distance_matrix.return_value = error_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
        )

        with pytest.raises(ApiFail) as exc_info:
            api.compute_route(params)
        assert exc_info.value.message == (
            "Invalid response: status not ok in"
            "\n{'status': 'INVALID_REQUEST', 'rows': []}"
        )

    def test_raises_error_if_wrong_row_count(self, mock_googlemaps_client: Any) -> None:
        api = DistanceMatrix("AIza fake key")
        wrong_rows_response = {
            "status": "OK",
            "rows": [],
        }
        mock_googlemaps_client.distance_matrix.return_value = wrong_rows_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
        )

        with pytest.raises(ApiFail, match="Invalid response: wrong row count"):
            api.compute_route(params)

    def test_raises_error_if_wrong_elements_count(
        self, mock_googlemaps_client: Any
    ) -> None:
        api = DistanceMatrix("AIza fake key")
        wrong_elements_response = {
            "status": "OK",
            "rows": [{"elements": []}],
        }
        mock_googlemaps_client.distance_matrix.return_value = wrong_elements_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
        )

        with pytest.raises(ApiFail, match="Invalid response: wrong elements count 0"):
            api.compute_route(params)

    def test_raises_error_if_element_status_not_ok(
        self, mock_googlemaps_client: Any
    ) -> None:
        api = DistanceMatrix("AIza fake key")
        element_error_response = {
            "status": "OK",
            "rows": [
                {
                    "elements": [
                        {
                            "status": "not ok",
                        }
                    ]
                }
            ],
        }
        mock_googlemaps_client.distance_matrix.return_value = element_error_response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
        )

        with pytest.raises(ApiFail, match="Invalid response: element status not ok"):
            api.compute_route(params)

    def test_raises_error_if_no_duration_in_traffic_for_car(
        self, mock_googlemaps_client: Any
    ) -> None:
        api = DistanceMatrix("AIza fake key")
        response_without_traffic = {
            "status": "OK",
            "rows": [
                {
                    "elements": [
                        {
                            "status": "OK",
                            "duration": {"value": 2763, "text": "46 mins"},
                            "distance": {"value": 55933, "text": "55.9 km"},
                        }
                    ]
                }
            ],
        }
        mock_googlemaps_client.distance_matrix.return_value = response_without_traffic
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.CAR,
            departure_time=datetime.now(),
            arrival_time=None,
        )

        with pytest.raises(ApiTimeout, match="Duration in traffic not computed"):
            api.compute_route(params)

    def test_uses_normal_duration_for_non_car_modes(
        self, mock_googlemaps_client: Any
    ) -> None:
        api = DistanceMatrix("AIza fake key")
        response = {
            "status": "OK",
            "rows": [
                {
                    "elements": [
                        {
                            "status": "OK",
                            "duration": {"value": 2763, "text": "46 mins"},
                            "distance": {"value": 55933, "text": "55.9 km"},
                        }
                    ]
                }
            ],
        }
        mock_googlemaps_client.distance_matrix.return_value = response
        params = GoogleRequestParameters(
            origin=GeoCoordinates(0, 0),
            destination=GeoCoordinates(1, 1),
            mode=TransportMode.PUBLIC_TRANSPORT,
            departure_time=datetime.now(),
            arrival_time=None,
        )

        result = api.compute_route(params)

        assert result.duration == 2763

    def test_compute_route_fails_with_proper_exceptions(
        self, fake_googlemaps_exception_raiser
    ):
        expected_exception_map = {
            googlemaps.exceptions.Timeout(): ApiTimeout,
            googlemaps.exceptions.ApiError("status"): ApiFail,
            googlemaps.exceptions.TransportError(): ApiFail,
            googlemaps.exceptions.HTTPError(500): ApiFail,
            googlemaps.exceptions._RetriableRequest(): ApiFail,
            googlemaps.exceptions._OverQueryLimit("status"): ApiFail,
        }
        googlemaps_apis = [
            (fake_googlemaps_exception_raiser(ge), tte)
            for ge, tte in expected_exception_map.items()
        ]
        origin = GeoCoordinates(latitude=4.7031, longitude=46.1146)
        destination = GeoCoordinates(latitude=4.8566, longitude=45.7668)
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.retries = 1
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=TransportMode.CAR,
        )

        for api, expected_exception in googlemaps_apis:
            travel_timer.google_routing_client = api
            with pytest.raises(expected_exception):
                travel_timer.compute_route(params)

    def test_timeout_message_is_explicit(self, fake_googlemaps_exception_raiser):
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.google_routing_client = fake_googlemaps_exception_raiser(
            googlemaps.exceptions.Timeout("badaboum")
        )
        travel_timer.retry_delay_in_seconds = 0
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 0.0)
        mode = TransportMode.WALK
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=mode,
        )

        with pytest.raises(ApiFail) as e:
            travel_timer.compute_route(params)
        assert e.value.message == "badaboum"

    def test_api_error_message_is_explicit(self, fake_googlemaps_exception_raiser):
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.google_routing_client = fake_googlemaps_exception_raiser(
            googlemaps.exceptions.ApiError("badstatus", "super message")
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 0.0)
        mode = TransportMode.WALK
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=mode,
        )

        with pytest.raises(ApiFail) as e:
            travel_timer.compute_route(params)
        assert e.value.message == "badstatus (super message)"

    def test_transport_error_message_is_explicit(
        self, fake_googlemaps_exception_raiser
    ):
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.google_routing_client = fake_googlemaps_exception_raiser(
            googlemaps.exceptions.TransportError()
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 0.0)
        mode = TransportMode.WALK
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=mode,
        )

        with pytest.raises(ApiFail) as e:
            travel_timer.compute_route(params)
        assert e.value.message == "An unknown error occurred."

    def test_http_error_message_is_explicit(self, fake_googlemaps_exception_raiser):
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.google_routing_client = fake_googlemaps_exception_raiser(
            googlemaps.exceptions.HTTPError(500)
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 0.0)
        mode = TransportMode.WALK
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=mode,
        )

        with pytest.raises(ApiFail) as e:
            travel_timer.compute_route(params)
        assert e.value.message == "HTTP Error: 500"

    def test_retriable_error_message_is_explicit(
        self, fake_googlemaps_exception_raiser
    ):
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.google_routing_client = fake_googlemaps_exception_raiser(
            googlemaps.exceptions._RetriableRequest("do retry")
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 0.0)
        mode = TransportMode.WALK
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=mode,
        )

        with pytest.raises(ApiFail) as e:
            travel_timer.compute_route(params)
        assert e.value.message == "do retry"

    def test_over_query_error_message_is_explicit(
        self, fake_googlemaps_exception_raiser
    ):
        travel_timer = DistanceMatrix("AIza fake key")
        travel_timer.google_routing_client = fake_googlemaps_exception_raiser(
            googlemaps.exceptions._OverQueryLimit("OVER_QUERY_LIMIT", "body error")
        )
        origin = GeoCoordinates(0.0, 1.0)
        destination = GeoCoordinates(1.0, 0.0)
        mode = TransportMode.WALK
        params = GoogleRequestParameters(
            origin=origin,
            destination=destination,
            mode=mode,
        )

        with pytest.raises(ApiFail) as e:
            travel_timer.compute_route(params)
        assert e.value.message == "OVER_QUERY_LIMIT (body error)"
