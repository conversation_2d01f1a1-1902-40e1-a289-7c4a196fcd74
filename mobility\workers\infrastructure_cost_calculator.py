from typing import List

from mobility.ir.cost import Cost, Cost<PERSON><PERSON>, CostPayer
from mobility.ir.geo_study import Localised
from mobility.ir.site import CoworkingSite, GeoSite
from mobility.quantity import Quantity, euros, euros_2020, meters, yearly
from mobility.workers.rent_price_computer import get_office_rent_price

CAR_PARKING_COST = 745 * euros_2020 / yearly
BICYCLE_PARKING_YEARLY_COST = 124 * euros_2020 / yearly
COWORKING_YEARLY_RENT_COST = 500 * euros * 12 / yearly
SURFACE_REQUIRED_BY_EMPLOYEE = 18 * meters * meters


class InfrastructureCostCalculator:
    def compute_car_parking_infrastructure_cost(self) -> Quantity:
        return CAR_PARKING_COST

    def compute_bicycle_parking_infrastructure_cost(self) -> Quantity:
        return BICYCLE_PARKING_YEARLY_COST

    def compute_office_rent_cost(self, site: GeoSite) -> Quantity:
        office_rent_price = get_office_rent_price(site)
        return SURFACE_REQUIRED_BY_EMPLOYEE * office_rent_price * 1.4

    def compute_coworking_rent_cost(self, site: CoworkingSite) -> Quantity:
        return COWORKING_YEARLY_RENT_COST

    def compute_destination_cost(self, destination: Localised) -> List[Cost]:
        if isinstance(destination, GeoSite):
            return [
                Cost(
                    kind=CostKind.OFFICE_RENT,
                    payer=CostPayer.COMPANY,
                    amount=self.compute_office_rent_cost(destination),
                )
            ]
        elif isinstance(destination, CoworkingSite):
            return [
                Cost(
                    kind=CostKind.COWORKING_RENT,
                    payer=CostPayer.COMPANY,
                    amount=self.compute_coworking_rent_cost(destination),
                )
            ]
        else:
            raise NotImplementedError(
                f"Could not compute destination cost for {destination}"
            )
