import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.factories import TravelTimeApiMaker
from api_abstraction.api.fastest_choice_travel_time_api import (
    FastestChoiceTravelTimeApi,
)
from api_abstraction.api.modal_travel_time_api import ModalTravelTimeApi
from api_abstraction.api.travel_time_api import TravelTimeApi
from api_abstraction.flight.flight_travel_time import FlightComputerApi
from api_abstraction.google.api import GoogleTravelTimeAPI
from api_abstraction.here.api import HereTravelTimeAPI
from api_abstraction.otp.api import OTPTravelTimeAPI
from mobility.ir.transport import TransportMode


@pytest.fixture
def sample_csv_path(tmp_path) -> str:
    csv_content = """iata_code,name,municipality,iso_country,coordinates
CDG,Charles <PERSON>,Paris,FR,"49.009722, 2.547778"
ORY,Orly,Paris,FR,"48.725278, 2.359444"
TLS,Blagnac,Toulouse,FR,"43.6291, 1.36382"
"""
    csv_file = tmp_path / "airports.csv"
    csv_file.write_text(csv_content)
    return str(csv_file)


class TestTravelTimeApiMaker:
    def test_can_make_trivial(self) -> None:
        factory = TravelTimeApiMaker("", "", EventReporter())

        travel_timer = factory.make("trivial")

        assert isinstance(travel_timer, TravelTimeApi)

    def test_can_make_distance(self) -> None:
        factory = TravelTimeApiMaker("", "", EventReporter())

        travel_timer = factory.make("distance")

        assert isinstance(travel_timer, TravelTimeApi)

    def test_can_make_default(self) -> None:
        factory = TravelTimeApiMaker(
            None,
            "AIzaGoogleLookingKey",
            EventReporter(),
            here_token="navitia-looking-key",
        )

        travel_timer = factory.make("default")

        assert isinstance(travel_timer, TravelTimeApi)

    def test_can_make_here(self) -> None:
        factory = TravelTimeApiMaker("", "", EventReporter(), here_token="a_token")

        travel_timer = factory.make("here")

        assert isinstance(travel_timer, HereTravelTimeAPI)

    def test_can_make_with_flight(self, sample_csv_path) -> None:
        factory = TravelTimeApiMaker(
            "", "AIzaGoogleLookingKey", EventReporter(), sample_csv_path
        )

        travel_timer = factory.make("with_flight")

        assert isinstance(travel_timer, TravelTimeApi)

    def test_with_flight_should_raise_on_missing_file(self) -> None:
        factory = TravelTimeApiMaker("", "", EventReporter())

        with pytest.raises(ValueError):
            factory.make("with_flight")

    def test_default_has_correct_type_for_each_mode(self) -> None:
        factory = TravelTimeApiMaker(
            "navitia-looking-key",
            "AIzaGoogleLookingKey",
            EventReporter(),
            here_token="coucou",
        )

        travel_timer = factory.make("default")

        expected_mode_kind = {
            TransportMode.WALK: OTPTravelTimeAPI,
            TransportMode.PUBLIC_TRANSPORT: FastestChoiceTravelTimeApi,
            TransportMode.CAR: GoogleTravelTimeAPI,
            TransportMode.BICYCLE: OTPTravelTimeAPI,
        }
        assert isinstance(travel_timer, ModalTravelTimeApi)
        for expected_mode, expected_api in expected_mode_kind.items():
            assert isinstance(travel_timer.mode_apis[expected_mode], expected_api)

    def test_isochrone_api_is_correct(self) -> None:
        factory = TravelTimeApiMaker(
            "navitia-looking-key",
            "AIzaGoogleLookingKey",
            EventReporter(),
            here_token="coucou",
        )

        travel_timer = factory.make("default")

        expected_mode_kind = {
            TransportMode.WALK: OTPTravelTimeAPI,
            TransportMode.PUBLIC_TRANSPORT: OTPTravelTimeAPI,
            TransportMode.CAR: HereTravelTimeAPI,
            TransportMode.BICYCLE: HereTravelTimeAPI,
        }
        assert isinstance(travel_timer, ModalTravelTimeApi)
        for expected_mode, expected_api in expected_mode_kind.items():
            assert isinstance(travel_timer.isochrone_apis[expected_mode], expected_api)

    def test_default_public_transport_is_fastest_of_main_apis(self) -> None:
        factory = TravelTimeApiMaker(
            "navitia-looking-key",
            "AIzaGoogleLookingKey",
            EventReporter(),
            here_token="coucou",
        )

        travel_timer = factory.make("default")

        assert isinstance(travel_timer, ModalTravelTimeApi)
        fastest_api = travel_timer.mode_apis[TransportMode.PUBLIC_TRANSPORT]
        assert isinstance(fastest_api, FastestChoiceTravelTimeApi)
        assert len(fastest_api.availables_apis) == 3
        assert isinstance(fastest_api.availables_apis[0], HereTravelTimeAPI)
        assert isinstance(fastest_api.availables_apis[1], GoogleTravelTimeAPI)
        assert isinstance(fastest_api.availables_apis[2], OTPTravelTimeAPI)

    def test_default_defines_an_api_for_each_modes(self) -> None:
        factory = TravelTimeApiMaker(
            "navitia-looking-key",
            "AIzaGoogleLookingKey",
            EventReporter(),
            here_token="coucou",
        )

        travel_timer = factory.make("default")

        assert isinstance(travel_timer, ModalTravelTimeApi)
        for mode in TransportMode.ground():
            assert isinstance(travel_timer.mode_apis[mode], TravelTimeApi)

    def test_factory_fails_on_unvalid_kind(self) -> None:
        factory = TravelTimeApiMaker(
            "navitia-looking-key", "AIzaGoogleLookingKey", EventReporter()
        )

        with pytest.raises(ValueError):
            factory.make("totally invalid kind of api")

    def test_with_flight_has_correct_type_for_each_mode(self, sample_csv_path) -> None:
        factory = TravelTimeApiMaker(
            "", "AIzaGoogleLookingKey", EventReporter(), sample_csv_path
        )

        travel_timer = factory.make("with_flight")

        expected_mode_kind = {
            TransportMode.WALK: OTPTravelTimeAPI,
            TransportMode.PUBLIC_TRANSPORT: GoogleTravelTimeAPI,
            TransportMode.BICYCLE: OTPTravelTimeAPI,
            TransportMode.CAR: GoogleTravelTimeAPI,
            TransportMode.AIRPLANE: FlightComputerApi,
        }
        assert isinstance(travel_timer, ModalTravelTimeApi)
        for expected_mode, expected_api in expected_mode_kind.items():
            assert isinstance(travel_timer.mode_apis[expected_mode], expected_api)

    def test_with_flight_should_raise_on_missing_google_key(self) -> None:
        factory = TravelTimeApiMaker("", "", EventReporter())

        with pytest.raises(ValueError):
            factory.make("with_flight")
