from typing import List

from mobility.ir.cost import Cost, Cost<PERSON><PERSON>, CostPayer
from mobility.ir.transport import TransportMode
from mobility.quantity import (
    Quantity,
    euros_2014,
    euros_2018,
    euros_2021,
    kilometers,
    trip,
    yearly,
)

HEALTH_COST_FOR_SOCIETY = 2980 * euros_2021 / yearly
HEALTH_IMPACT_COST_OF_ACTIVE_MODE = 0.484 * euros_2014 / kilometers
HEALTH_INSURANCE_COST_FOR_EMPLOYEE = 455 * euros_2018 / yearly
HEALTH_INSURANCE_COST_FOR_COMPANY = 774 * euros_2018 / yearly
HEALTH_INSURANCE_COST_FOR_EMPLOYEE_WITH_BENEFITS = 410 * euros_2018 / yearly
HEALTH_INSURANCE_COST_FOR_COMPANY_WITH_BENEFITS = 697 * euros_2018 / yearly


class HealthCostCalculator:
    def compute_health_gain_from_active_trip_distance(
        self, distance: Quantity
    ) -> Quantity:
        return -(distance / trip) * HEALTH_IMPACT_COST_OF_ACTIVE_MODE

    def compute_health_cost_for_society(
        self, transport_mode: TransportMode, distance: Quantity
    ) -> List[Cost]:
        if transport_mode in [TransportMode.WALK, TransportMode.BICYCLE]:
            cost = self.compute_health_gain_from_active_trip_distance(distance)
            return [
                Cost(payer=CostPayer.SOCIETY, kind=CostKind.HEALTH_IMPACT, amount=cost)
            ]
        else:
            return []

    def compute_health_insurance_costs(
        self, nb_employees: int, nb_employees_with_benefit: int
    ) -> List[Cost]:
        cost_for_company = (
            (nb_employees - nb_employees_with_benefit)
            * HEALTH_INSURANCE_COST_FOR_COMPANY
            + nb_employees_with_benefit
            * HEALTH_INSURANCE_COST_FOR_COMPANY_WITH_BENEFITS
        )
        cost_for_employee = (
            (nb_employees - nb_employees_with_benefit)
            * HEALTH_INSURANCE_COST_FOR_EMPLOYEE
            + nb_employees_with_benefit
            * HEALTH_INSURANCE_COST_FOR_EMPLOYEE_WITH_BENEFITS
        )
        return [
            Cost(
                payer=CostPayer.COMPANY,
                amount=cost_for_company,
                kind=CostKind.HEALTH_INSURANCE,
            ),
            Cost(
                payer=CostPayer.EMPLOYEE,
                amount=cost_for_employee,
                kind=CostKind.HEALTH_INSURANCE,
            ),
        ]
