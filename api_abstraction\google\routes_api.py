from typing import Any, Dict, Optional

import google.api_core.exceptions as routes_api_exceptions
from google.maps.routing_v2 import ComputeRoutesResponse, Route, RoutesClient

from api_abstraction.api.api import ApiFail, ApiTimeout
from api_abstraction.api.travel_time_api import JourneyAttribute
from api_abstraction.google.base_google_api import (
    GoogleClientAPI,
    GoogleRequestParameters,
)
from mobility.ir.transport import TransportMode


class RoutesAPI(GoogleClientAPI):
    def __init__(self, json_key_path: str) -> None:
        super().__init__()
        self.google_routing_client = RoutesClient.from_service_account_file(
            json_key_path
        )

    def compute_route(self, parameters: GoogleRequestParameters) -> JourneyAttribute:
        output_fields = "routes.distanceMeters,routes.duration"
        field_mask_headers = [("x-goog-fieldmask", output_fields)]
        req_parameters = self._format_input_parameters(parameters)
        try:
            route_result = self.google_routing_client.compute_routes(
                req_parameters, metadata=field_mask_headers
            )
        except routes_api_exceptions.DeadlineExceeded as e:
            raise ApiTimeout(str(e))
        except (
            routes_api_exceptions.GoogleAPICallError,
            routes_api_exceptions.RetryError,
            routes_api_exceptions.ServiceUnavailable,
            routes_api_exceptions.TooManyRequests,
            routes_api_exceptions.Unknown,
            routes_api_exceptions.BadRequest,
        ) as e:
            raise ApiFail(str(e))
        return self._format_result(route_result, parameters.mode)

    def _format_result(
        self, result: ComputeRoutesResponse, mode: Optional[TransportMode]
    ) -> JourneyAttribute:
        if len(result.routes) != 1:
            raise ApiFail(f"Invalid response: wrong routes count {len(result.routes)}")
        element = result.routes[0]
        return JourneyAttribute(
            duration=self._extract_duration_from_element(element),
            distance=self._extract_distance_from_element(element),
            emission=None,
        )

    def _extract_duration_from_element(self, element: Route) -> int:
        return element.duration.seconds

    def _extract_distance_from_element(self, element: Route) -> int:
        return element.distance_meters

    def _map_google_mode(self, mode: TransportMode) -> str:
        mode_mapping = {
            TransportMode.WALK: "WALK",
            TransportMode.PUBLIC_TRANSPORT: "TRANSIT",
            TransportMode.CAR: "DRIVE",
            TransportMode.ELECTRIC_CAR: "DRIVE",
            TransportMode.CARPOOLING: "DRIVE",
            TransportMode.MOTORCYCLE: "TWO_WHEELER",
            TransportMode.ELECTRIC_MOTORCYCLE: "TWO_WHEELER",
            TransportMode.BICYCLE: "BICYCLE",
        }
        if mode not in mode_mapping:
            raise ApiFail(f"Mode {mode.value} unavailable in mapping")
        return mode_mapping[mode]

    def _map_route_api_traffic_model(self, traffic_model: str) -> str:
        return traffic_model.upper()

    def _add_car_like_specific_parameters(
        self,
        parameters: Dict[str, Any],
        google_request_parameters: GoogleRequestParameters,
    ) -> None:
        if google_request_parameters.mode in self.car_like_modes:
            parameters["routing_preference"] = (
                google_request_parameters.routing_preference
            )
            parameters["traffic_model"] = self._map_route_api_traffic_model(
                google_request_parameters.traffic_model
            )

    def _format_input_parameters(
        self, parameters: GoogleRequestParameters
    ) -> Dict[str, Any]:
        formatted_request_parameters = {
            "origin": {
                "location": {
                    "lat_lng": {
                        "latitude": parameters.origin.latitude,
                        "longitude": parameters.origin.longitude,
                    }
                }
            },
            "destination": {
                "location": {
                    "lat_lng": {
                        "latitude": parameters.destination.latitude,
                        "longitude": parameters.destination.longitude,
                    }
                }
            },
            "travel_mode": self._map_google_mode(parameters.mode),
            "departure_time": parameters.departure_time,
            "arrival_time": parameters.arrival_time,
            "language_code": "fr",
            "units": "METRIC",
        }
        self._add_car_like_specific_parameters(formatted_request_parameters, parameters)
        return formatted_request_parameters
