from unittest.mock import Mock, patch

import pytest
import requests

from api_abstraction.api.api import ApiFail, ApiTimeout
from api_abstraction.here.base_here_api import BaseHereAP<PERSON>
from mobility.ir.transport import TransportMode


class EmptyUrlAPI(BaseHereAPI):
    pass


class ValidTestBaseHereAPI(BaseHereAPI):
    display_name = "Test API"
    root_url = "https://here.com"

    def _format_input_parameters(self, parameters):
        return {}

    def _format_result(self, result):
        return result


class TestBaseHereAPI:
    def test_init_without_root_url(self) -> None:
        with pytest.raises(ApiFail, match="url is not specified"):
            EmptyUrlAPI("test_api_key")

    @patch("api_abstraction.here.base_here_api.requests.get")
    def test_make_request_success(self, mock_get: Mock) -> None:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"test": "data"}
        mock_get.return_value = mock_response
        api = ValidTestBaseHereAPI("test_api_key")

        result = api._make_request("https://here.com", {"param": "value"})

        mock_get.assert_called_once_with(
            "https://here.com", params={"param": "value", "apikey": "test_api_key"}
        )
        assert result == {"test": "data"}

    @patch("api_abstraction.here.base_here_api.requests.get")
    def test_make_request_status_code_error(self, mock_get: Mock) -> None:
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_get.return_value = mock_response

        api = ValidTestBaseHereAPI("test_api_key")

        with pytest.raises(ApiFail, match="Test API request failed with status 400"):
            api._make_request("https://here.com", {})

    @patch("api_abstraction.here.base_here_api.requests.get")
    def test_make_request_timeout(self, mock_get: Mock) -> None:
        mock_get.side_effect = requests.Timeout("Connection timed out")

        api = ValidTestBaseHereAPI("test_api_key")

        with pytest.raises(ApiTimeout, match="Test API request timed out"):
            api._make_request("https://here.com", {})

    @patch("api_abstraction.here.base_here_api.requests.get")
    def test_make_request_general_exception(self, mock_get: Mock) -> None:
        mock_get.side_effect = requests.RequestException("Connection error")

        api = ValidTestBaseHereAPI("test_api_key")

        with pytest.raises(ApiFail, match="Test API request failed: Connection error"):
            api._make_request("https://here.com", {})

    @patch("api_abstraction.here.base_here_api.requests.get")
    def test_make_request_json_parse_error(self, mock_get: Mock) -> None:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = ValueError("Invalid JSON")
        mock_get.return_value = mock_response

        api = ValidTestBaseHereAPI("test_api_key")

        with pytest.raises(
            ApiFail, match="Failed to parse Test API response: Invalid JSON"
        ):
            api._make_request("https://here.com", {})

    @pytest.mark.parametrize(
        "mode,expected_here_mode",
        [
            (TransportMode.CAR, "car"),
            (TransportMode.BICYCLE, "bicycle"),
            (TransportMode.WALK, "pedestrian"),
            (TransportMode.MOTORCYCLE, "scooter"),
            (TransportMode.CARPOOLING, "car"),
            (TransportMode.ELECTRIC_CAR, "car"),
        ],
    )
    def test_maps_transport_modes_correctly(self, mode, expected_here_mode) -> None:
        api = ValidTestBaseHereAPI("test_api_key")
        assert api._map_here_mode(mode) == expected_here_mode

    def test_raises_error_for_unsupported_mode(self) -> None:
        api = ValidTestBaseHereAPI("test_api_key")

        with pytest.raises(ApiFail, match="Mode AIRPLANE unavailable in mapping"):
            api._map_here_mode(TransportMode.AIRPLANE)

    def test_map_here_mode_public_transport_should_raise(self) -> None:
        api = ValidTestBaseHereAPI("test_api_key")

        with pytest.raises(ApiFail, match="Public transport routing has its own API"):
            api._map_here_mode(TransportMode.PUBLIC_TRANSPORT)
