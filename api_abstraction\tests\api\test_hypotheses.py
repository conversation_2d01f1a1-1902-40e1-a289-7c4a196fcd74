from datetime import date, datetime, timedelta

import pytest

from api_abstraction.api.date_iterators import DateTimeInterval
from api_abstraction.api.hypotheses import closest_tuesday_8_30_in_interval


class TestHypotheses:
    def test_should_fail_2_week_before_expiration_as_a_canary(self) -> None:
        """When this test fails, it is time to update the list of working days"""
        in_two_weeks = datetime.now() + timedelta(weeks=2)

        day = closest_tuesday_8_30_in_interval(
            DateTimeInterval(in_two_weeks, None), date.today()
        )

        assert day.weekday() == 1

    def test_should_fail_because_we_will_never_bother_to_input_ten_years_of_working_days(
        self,
    ) -> None:
        in_ten_years = datetime.now() + timedelta(weeks=50 * 10)

        with pytest.raises(ValueError):
            closest_tuesday_8_30_in_interval(
                DateTimeInterval(in_ten_years, None), in_ten_years
            )
