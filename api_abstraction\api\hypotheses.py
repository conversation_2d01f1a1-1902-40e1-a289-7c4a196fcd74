from datetime import date, datetime
from typing import List, Tuple

from api_abstraction.api.date_iterators import (
    DateTimeInterval,
    DateTimeIntervals,
    DayInInterval,
    DayOfWeek,
)

french_working_weeks: List[Tuple[datetime, datetime]] = [
    (datetime.fromisoformat(low), datetime.fromisoformat(high))
    for low, high in [
        ("2018-09-03", "2018-10-20"),
        ("2018-11-05", "2018-12-22"),
        ("2019-01-07", "2019-02-09"),
        ("2019-03-11", "2019-04-06"),
        ("2019-05-06", "2019-05-29"),
        ("2019-06-03", "2019-07-06"),
        ("2019-09-02", "2019-10-19"),
        ("2019-11-04", "2019-12-21"),
        ("2020-01-06", "2020-02-08"),
        ("2020-03-09", "2020-04-04"),
        ("2020-04-27", "2020-05-20"),
        ("2020-05-25", "2020-07-04"),
        ("2020-09-01", "2020-10-17"),
        ("2020-11-02", "2020-12-19"),
        ("2021-01-04", "2021-02-06"),
        ("2021-03-08", "2021-04-10"),
        ("2021-05-10", "2021-05-12"),
        ("2021-05-17", "2021-07-06"),
        ("2021-09-02", "2021-10-23"),
        ("2021-11-08", "2021-12-18"),
        ("2022-01-03", "2022-02-05"),
        ("2022-03-07", "2022-04-09"),
        ("2022-05-09", "2022-05-25"),
        ("2022-05-30", "2022-07-07"),
        ("2022-09-01", "2022-10-22"),
        ("2022-11-07", "2022-12-17"),
        ("2023-01-03", "2023-02-04"),
        ("2023-03-06", "2023-04-08"),
        ("2023-05-09", "2023-05-17"),
        ("2023-05-22", "2023-07-08"),
        ("2023-09-04", "2023-10-21"),
        ("2023-11-06", "2023-12-23"),
        ("2024-01-08", "2024-02-10"),
        ("2024-03-11", "2024-04-06"),
        ("2024-05-06", "2024-07-06"),
        ("2024-09-02", "2024-10-19"),
        ("2024-11-04", "2024-12-21"),
        ("2025-01-06", "2025-02-08"),
        ("2025-03-10", "2025-04-05"),
        ("2025-04-28", "2025-07-05"),
        ("2025-09-01", "2025-10-18"),
        ("2025-11-03", "2025-12-20"),
        ("2026-01-05", "2026-02-07"),
        ("2026-03-09", "2026-04-04"),
        ("2026-04-27", "2026-07-04"),
    ]
]

french_public_holidays: List[datetime] = [
    datetime.fromisoformat(day)
    for day in [
        "2020-01-01",
        "2020-04-13",
        "2020-05-01",
        "2020-05-08",
        "2020-05-21",
        "2020-06-01",
        "2020-07-14",
        "2020-08-15",
        "2020-11-01",
        "2020-11-11",
        "2020-12-25",
        "2021-01-01",
        "2021-04-05",
        "2021-05-01",
        "2021-05-08",
        "2021-05-13",
        "2021-05-24",
        "2021-07-14",
        "2021-08-15",
        "2021-11-01",
        "2021-11-11",
        "2021-12-25",
        "2022-01-01",
        "2022-04-18",
        "2022-05-01",
        "2022-05-08",
        "2022-05-26",
        "2022-06-06",
        "2022-07-14",
        "2022-08-15",
        "2022-11-01",
        "2022-11-11",
        "2022-12-25",
        "2023-01-01",
        "2023-04-10",
        "2023-05-01",
        "2023-05-08",
        "2023-05-18",
        "2023-05-29",
        "2023-07-14",
        "2023-08-15",
        "2023-11-01",
        "2023-11-11",
        "2023-12-25",
        "2024-01-01",
        "2024-04-01",
        "2024-05-01",
        "2024-05-08",
        "2024-05-09",
        "2024-05-20",
        "2024-07-14",
        "2024-08-15",
        "2024-11-01",
        "2024-11-11",
        "2024-12-25",
    ]
]


def closest_tuesday_8_30_in_interval(
    interval: DateTimeInterval, iteration_start: date
) -> datetime:
    working_days = DateTimeIntervals(
        [DateTimeInterval(low, high) for low, high in french_working_weeks]
    )
    for holiday in french_public_holidays:
        working_days = working_days.remove_day(holiday)
    return next(
        DayInInterval(
            interval=working_days,
            strict=True,
            dates=DayInInterval(
                interval=interval,
                dates=DayOfWeek(
                    start_day=iteration_start, weekday=1, hour=8, minute=30
                ),
            ),
        )
    )
