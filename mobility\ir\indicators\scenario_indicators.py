from collections import defaultdict
from typing import Dict, List, Optional, Set, Tuple

import numpy

import mobility.m_list as m_list
from mobility import config
from mobility.converters.accessibility import AccessibilitySlice
from mobility.converters.indicators.carpool import compute_carpool_indicators
from mobility.converters.indicators.clustering import compute_clustering_indicators
from mobility.converters.indicators.mode_shift_scenario import (
    ModeShiftScenarioIndicators,
    ModeShiftSpecification,
    compute_mode_shift_scenario_indicators,
)
from mobility.converters.indicators.remote_scenario import (
    compute_remote_scenario_indicators,
)
from mobility.converters.indicators.scenario import compute_nb_employees_per_profile
from mobility.converters.indicators.scores import compute_scenario_scores
from mobility.converters.indicators.staggered_hours import (
    compute_staggered_hours_indicators,
)
from mobility.funky import ImmutableDict, filter_on_bounds, get_some
from mobility.ir.cost import IndividualCosts
from mobility.ir.employee import GeoEmployee
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.indicators.carpool_indicators import CarpoolIndicators
from mobility.ir.indicators.clustering_indicators import ClusteringIndicators
from mobility.ir.indicators.coworking_indicators import (
    CoworkingIndicatorBuilder,
    CoworkingIndicators,
)
from mobility.ir.indicators.remote_scenario_indicators import RemoteScenarioIndicators
from mobility.ir.indicators.staggered_hours_indicators import (
    StaggeredHoursIndicators,
    StaggeredHoursIndicatorsSpecification,
)
from mobility.ir.indicators.study_indicators import shorten_nickname
from mobility.ir.study import ConsolidatedScenario, CoworkingScenario, GeoSite
from mobility.ir.transport import TransportMode
from mobility.quantity import (
    Quantity,
    car_parking_spot,
    gramEC,
    minutes,
    seconds,
    tonEC,
    trip,
    yearly,
)
from mobility.workers.cost_computer import CostComputer
from mobility.workers.zfe_impact_computer import ZFEImpactCalendar, ZFEImpactComputer


class ScenarioIndicators:
    def __init__(
        self,
        scenario: ConsolidatedScenario,
        coworking_scenario: CoworkingScenario,
    ):
        self.cost_computer = CostComputer()
        self.scenario = scenario
        self.nb_employees = len(self.scenario.commutes)
        self.coworking_scenario = coworking_scenario
        self.original_nickname = self.scenario.data.nickname
        self.nickname = shorten_nickname(self.scenario.data.nickname)
        self.average_travel_time = self.construct_average_travel_time_any_mode()
        self.average_travel_time_quantity = self.average_travel_time * seconds
        self.addresses = self.construct_addresses()
        self.geo_addresses = self.construct_geo_adresses()
        self.scores = compute_scenario_scores(self.scenario)
        self._staggered_hours_indicators: Optional[List[StaggeredHoursIndicators]] = (
            None
        )
        self._clustering_indicators: Optional[ClusteringIndicators] = None
        self._coworking_indicators: Optional[CoworkingIndicators] = None
        self._carpool_indicators: Optional[CarpoolIndicators] = None
        self.max_nb_remote_workers = self.compute_max_nb_remote_workers()
        self._remote_scenarios: Optional[List[RemoteScenarioIndicators]] = None
        self._public_transport_shift_scenarios: Optional[
            List[ModeShiftScenarioIndicators]
        ] = None
        self._bike_shift_scenarios: Optional[List[ModeShiftScenarioIndicators]] = None
        self.costs = self.compute_costs()
        self.zfe_impact_calendar = self.compute_zfe_impact_calendar()
        self.active_zfes = self.make_list_of_active_zfes()
        self.max_zfe_impact_year = max(
            self.zfe_impact_calendar,
            key=lambda year: self.zfe_impact_calendar[year].percent_workers_impacted,
        )
        self.employees_count_per_profile = compute_nb_employees_per_profile(
            list(scenario.commutes)
        )
        self.parking_spots = self.compute_required_parking_spots()

    def compute_zfe_impact_calendar(self) -> ZFEImpactCalendar:
        territories_dir = config["mobility"].get("territories_dir", ".")
        zfe_impact_computer = ZFEImpactComputer(territories_dir)
        impact_by_site = {}
        for site, employees_commutes in self.scenario.get_destinations().items():
            nb_non_drivers = len(
                [e for e, c in employees_commutes if c.best_mode != TransportMode.CAR]
            )
            drivers_citycodes = [
                e.address_details.citycode
                for e, c in employees_commutes
                if c.best_mode == TransportMode.CAR
            ]
            impact_by_site[site] = (
                zfe_impact_computer.compute_zfe_impact_for_company_and_employees(
                    site.address_details.citycode,
                    nb_non_drivers,
                    drivers_citycodes,
                )
            )
        global_impact: ZFEImpactCalendar = {}
        for impact_calendar in impact_by_site.values():
            for year, zfe_impact in impact_calendar.items():
                if year in global_impact:
                    global_impact[year] += zfe_impact
                else:
                    global_impact[year] = zfe_impact
        return get_some(global_impact)

    def make_list_of_active_zfes(self) -> List[str]:
        max_impact_by_zfe = {}
        for zfe_impact in self.zfe_impact_calendar.values():
            for zfe_name, impact in zfe_impact.nb_impact_by_zfe.items():
                if zfe_name not in max_impact_by_zfe:
                    max_impact_by_zfe[zfe_name] = impact
                else:
                    max_impact_by_zfe[zfe_name] = max(
                        impact, max_impact_by_zfe[zfe_name]
                    )
        return sorted(
            max_impact_by_zfe,
            key=lambda zfe_name: max_impact_by_zfe[zfe_name],
            reverse=True,
        )

    def compute_costs(self) -> IndividualCosts:
        base_costs = IndividualCosts(
            ImmutableDict(
                {
                    commute.origin: tuple(
                        [c for c in self.cost_computer.compute_commute_cost(commute)]
                    )
                    for commute in self.scenario.commutes
                }
            )
        )
        nb_employees = len(self.scenario.commutes)
        health_insurance_costs = (
            self.cost_computer.health.compute_health_insurance_costs(nb_employees, 0)
        )
        costs = base_costs.add_costs_divided_accross_employees(health_insurance_costs)
        return costs

    @property
    def staggered_hours_indicators(self) -> List[StaggeredHoursIndicators]:
        if self._staggered_hours_indicators is None:
            self._staggered_hours_indicators = (
                self.construct_staggered_hours_indicators()
            )
        return self._staggered_hours_indicators

    @property
    def clustering_indicators(self) -> ClusteringIndicators:
        if self._clustering_indicators is None:
            self._clustering_indicators = compute_clustering_indicators(self.scenario)
        return self._clustering_indicators

    @property
    def coworking_indicators(self) -> CoworkingIndicators:
        if self._coworking_indicators is None:
            self._coworking_indicators = CoworkingIndicatorBuilder(
                self.scenario, self.coworking_scenario
            ).build()
        return self._coworking_indicators

    @property
    def carpool_indicators(self) -> CarpoolIndicators:
        if self._carpool_indicators is None:
            self._carpool_indicators = compute_carpool_indicators(self.scenario)
        return self._carpool_indicators

    def get_remote_scenarios(self) -> List[RemoteScenarioIndicators]:
        if self._remote_scenarios is None:
            return self._build_remote_scenarios()
        return self._remote_scenarios

    def _build_remote_scenarios(self) -> List[RemoteScenarioIndicators]:
        self._remote_scenarios = []
        bounds = [0 * minutes, 30 * minutes, 45 * minutes, 60 * minutes]
        names = [f"Scénario {i + 1}" for i in range(len(bounds))]
        for min_bound, nickname in zip(bounds, names):
            self._remote_scenarios.append(
                compute_remote_scenario_indicators(nickname, self.scenario, min_bound)
            )
        return self._remote_scenarios

    def compute_max_nb_remote_workers(self) -> int:
        return sum(c.origin.remote for c in self.scenario.commutes)

    def get_bike_shift_scenarios(self) -> List[ModeShiftScenarioIndicators]:
        if self._bike_shift_scenarios is None:
            return self._build_bike_shift_scenarios()
        return self._bike_shift_scenarios

    def _build_bike_shift_scenarios(self) -> List[ModeShiftScenarioIndicators]:
        self._bike_shift_scenarios = []
        delta_times = [0 * minutes, 10 * minutes]
        resulting_time = [20 * minutes, 30 * minutes]
        name = ["Modéré", "Ambitieux"]
        for max_delta_time, max_resulting_time, nickname in zip(
            delta_times, resulting_time, name
        ):
            spec = ModeShiftSpecification(
                max_delta_time=max_delta_time,
                nickname=f"Scénario {nickname}",
                modes_to_shift_from=[
                    TransportMode.CAR,
                    TransportMode.MOTORCYCLE,
                    TransportMode.CARPOOLING,
                    TransportMode.ELECTRIC_CAR,
                    TransportMode.PUBLIC_TRANSPORT,
                ],
                mode_to_shift_to=TransportMode.BICYCLE,
                max_resulting_time=max_resulting_time,
            )
            bike_indicator = compute_mode_shift_scenario_indicators(self.scenario, spec)
            self._bike_shift_scenarios.append(bike_indicator)
        return self._bike_shift_scenarios

    def get_public_transport_shift_scenarios(self) -> List[ModeShiftScenarioIndicators]:
        if self._public_transport_shift_scenarios is None:
            return self._build_public_transport_shift_scenarios()
        return self._public_transport_shift_scenarios

    def _build_public_transport_shift_scenarios(
        self,
    ) -> List[ModeShiftScenarioIndicators]:
        self._public_transport_shift_scenarios = []
        delta_times = [5 * minutes, 15 * minutes]
        names = ["Modéré", "Ambitieux"]
        for max_delta_time, name in zip(delta_times, names):
            spec = ModeShiftSpecification(
                max_delta_time=max_delta_time,
                nickname=f"Scénario {name}",
                modes_to_shift_from=[
                    TransportMode.CAR,
                    TransportMode.MOTORCYCLE,
                    TransportMode.CARPOOLING,
                    TransportMode.ELECTRIC_CAR,
                ],
                mode_to_shift_to=TransportMode.PUBLIC_TRANSPORT,
                max_resulting_time=None,
            )
            indicator = compute_mode_shift_scenario_indicators(self.scenario, spec)
            self._public_transport_shift_scenarios.append(indicator)
        return self._public_transport_shift_scenarios

    def construct_staggered_hours_indicators(self) -> List[StaggeredHoursIndicators]:
        indicators = []
        for time in gather_alternative_arrival_time(self.scenario):
            spec = StaggeredHoursIndicatorsSpecification(time)
            indicator = compute_staggered_hours_indicators(self.scenario, spec)
            indicators.append(indicator)
        return indicators

    def has_staggered_hours(self) -> bool:
        return len(self.staggered_hours_indicators) > 0

    def has_remote_working(self) -> bool:
        return any(
            scenario.nb_remote_workers > 0 for scenario in self.get_remote_scenarios()
        )

    def get_quickest_staggered_hours_indicator(self) -> StaggeredHoursIndicators:
        non_ref_indicators = [
            i for i in self.staggered_hours_indicators if not i.is_reference
        ]
        return min(non_ref_indicators, key=lambda i: i.mean_duration)

    def get_reference_staggered_hours_indicator(
        self,
    ) -> Optional[StaggeredHoursIndicators]:
        for i in self.staggered_hours_indicators:
            if i.is_reference:
                return i
        return None

    def construct_nickname(self) -> str:
        nickname = self.scenario.data.nickname
        if len(nickname) > 12:
            nickname = f"{nickname[:12]}…"
        return nickname

    def construct_geo_adresses(self) -> Set[GeoCoordinates]:
        return {c.destination.coordinates for c in self.scenario.commutes}

    def construct_addresses(self) -> Set[str]:
        return {c.destination.address for c in self.scenario.commutes}

    def get_single_address(self) -> str:
        if len(self.addresses) == 1:
            address, *_ = self.addresses
            return address
        else:
            raise ValueError(f"No single site in scenario {self.scenario}")

    def get_nb_employees_in(self, accessibility_slice: AccessibilitySlice) -> int:
        min_bound = accessibility_slice.lower_bound
        max_bound = accessibility_slice.upper_bound
        return self.get_nb_employees_in_interval(min_bound, max_bound)

    def get_nb_employees_in_interval(
        self, min_bound: Optional[int] = None, max_bound: Optional[int] = None
    ) -> int:
        return self.get_nb_employees_in_interval_per_mode(
            min_bound, max_bound, *TransportMode
        )

    def get_nb_employees_in_interval_by_forced_mode(
        self,
        min_bound: Optional[int],
        max_bound: Optional[int],
        forced_mode: TransportMode,
    ) -> int:
        sites = list(self.scenario.get_destinations())
        return self.get_nb_employees_in_interval_by_forced_mode_by_site(
            min_bound,
            max_bound,
            forced_mode,
            sites,
        )

    def get_nb_employees_in_interval_by_forced_mode_by_site(
        self,
        min_bound: Optional[int],
        max_bound: Optional[int],
        forced_mode: TransportMode,
        sites: List[GeoSite],
    ) -> int:
        durations = [
            commute.data.duration[forced_mode]
            for commute in self.scenario.commutes
            if forced_mode in commute.data.duration and commute.destination in sites
        ]
        return len(filter_on_bounds(durations, min_bound, max_bound))

    def get_nb_employees_in_interval_per_mode(
        self,
        min_bound: Optional[int],
        max_bound: Optional[int],
        *allowed_modes: TransportMode,
    ) -> int:
        durations = self._extract_durations_from_commutes(*allowed_modes)
        return len(filter_on_bounds(durations, min_bound, max_bound))

    def _compute_average_time(self, *allowed_modes: TransportMode) -> Optional[float]:
        durations = self._extract_durations_from_commutes(*allowed_modes)
        return m_list.apply_on_non_trivial(lambda x: float(numpy.mean(x)), durations)

    def compute_median_time(self, *allowed_modes: TransportMode) -> Optional[float]:
        return self.compute_percentile(50, *allowed_modes)

    def compute_percentile(
        self, percentile: int, *allowed_modes: TransportMode
    ) -> Optional[float]:
        modes = get_default_transport_modes_if_empty(allowed_modes)
        durations = self._extract_durations_from_commutes(*modes)
        if len(durations) != 0:
            return float(numpy.percentile(durations, percentile))
        else:
            return None

    def compute_travel_times(self, *allowed_modes: TransportMode) -> List[int]:
        modes = get_default_transport_modes_if_empty(allowed_modes)
        return self._extract_durations_from_commutes(*modes)

    def construct_average_travel_time_any_mode(self) -> float:
        average = self.construct_average_travel_time(*TransportMode)
        if average is None:
            raise ValueError(f"None average time for scenario {self.scenario}")
        return average

    def construct_average_travel_time(
        self, *transport_mode: TransportMode
    ) -> Optional[float]:
        average_time = self._compute_average_time(*transport_mode)
        return average_time

    def compute_employees_by_postcode_and_mode(
        self,
    ) -> Dict[str, Dict[TransportMode, List[GeoEmployee]]]:
        employees_by_postcode_by_mode: Dict[
            str, Dict[TransportMode, List[GeoEmployee]]
        ] = defaultdict(lambda: defaultdict(list))
        for c in self.scenario.commutes:
            employees_by_postcode_by_mode[c.origin.address_details.postcode][
                c.data.best_mode
            ].append(c.origin)
        return dict(employees_by_postcode_by_mode)

    def count_employees_repartition_per_mode(self) -> Dict[TransportMode, int]:
        return {mode: self.count_employees_per_mode(mode) for mode in TransportMode}

    def count_employees_per_mode(self, *allowed_modes: TransportMode) -> int:
        return len(
            {
                c.origin
                for c in self.scenario.commutes
                if c.data.best_mode in allowed_modes
            }
        )

    def count_employees_repartition_per_declared_mode(
        self,
    ) -> Dict[Optional[TransportMode], int]:
        mode_counter: Dict[Optional[TransportMode], int] = defaultdict(int)
        for employee in self.scenario.get_origins():
            mode_counter[employee.transport_mode] += 1
        return mode_counter

    def count_employees_with_inferred_mode(self) -> int:
        return sum(
            self.count_employees_per_inferred_mode(mode) for mode in TransportMode
        )

    def count_employees_with_inferred_mode_repartition_per_mode(
        self,
    ) -> Dict[TransportMode, int]:
        return {
            mode: self.count_employees_per_inferred_mode(mode) for mode in TransportMode
        }

    def count_employees_per_inferred_mode(self, *allowed_modes: TransportMode) -> int:
        return len(
            {
                c.origin
                for c in self.scenario.commutes
                if c.data.best_mode in allowed_modes and c.origin.transport_mode is None
            }
        )

    def _compute_distribution(
        self,
        bucket_size: int,
        last_bucket: int,
        *allowed_modes: TransportMode,
    ) -> Dict[Tuple[int, int], int]:
        durations = self._extract_durations_from_commutes(*allowed_modes)
        return self._compute_distribution_of_durations(
            durations, bucket_size, last_bucket
        )

    def _extract_durations_from_commutes(
        self, *allowed_modes: TransportMode
    ) -> List[int]:
        return [
            commute.data.duration[commute.data.best_mode]
            for commute in self.scenario.commutes
            if commute.data.best_mode in commute.data.duration
            and commute.data.best_mode in allowed_modes
        ]

    @staticmethod
    def _compute_distribution_of_durations(
        durations: List[int], bucket_size: int, last_bucket: int
    ) -> Dict[Tuple[int, int], int]:
        buckets = list(range(0, last_bucket + bucket_size, bucket_size))
        if len(durations) > 0 and max(durations) > last_bucket:
            buckets.append(max(durations))
        distribution, _ = numpy.histogram(durations, bins=buckets)
        return {
            (min_bound, max_bound): population
            for min_bound, max_bound, population in zip(
                buckets[:-1], buckets[1:], distribution
            )
        }

    def get_employees_geocoordinates(
        self, *transport_modes: TransportMode
    ) -> List[GeoCoordinates]:
        modes = get_default_transport_modes_if_empty(transport_modes)
        return [
            c.origin.coordinates
            for c in self.scenario.commutes
            if c.data.best_mode in modes
        ]

    def get_sites(self) -> List[GeoSite]:
        """Get all sites in the scenario, sorted by decreasing number of commutes"""
        sites = self.scenario.get_destinations()
        unsorted_sites = list(sites)
        return sorted(unsorted_sites, key=lambda s: len(sites[s]), reverse=True)

    def get_employees(self) -> List[GeoEmployee]:
        """Get all employees in the scenario, sorted by increasing id"""
        employees = self.scenario.get_origins()
        unsorted_employees = list(employees)
        return sorted(unsorted_employees, key=lambda s: s.id, reverse=True)

    def get_site_workforce(self, site: GeoSite) -> int:
        destinations = self.scenario.get_destinations()
        origins_and_data: Set = destinations.get(site, set())
        return len(origins_and_data)

    def get_sites_geocoordinates(self) -> Dict[str, GeoCoordinates]:
        sites_coords = {}
        sites = self.get_sites()
        for site in sorted(sites, key=lambda s: s.id):
            sites_coords[site.nickname] = site.coordinates
        return sites_coords

    def get_carbon_emission(self, *transport_modes: TransportMode) -> Quantity:
        modes = get_default_transport_modes_if_empty(transport_modes)
        carbon_emissions = 0
        for commute in self.scenario.commutes:
            transport_mode = commute.data.best_mode
            if transport_mode in modes:
                carbon_emissions += commute.data.emission[transport_mode]
        return carbon_emissions * gramEC / trip

    def get_yearly_carbon_emission_per_employee(
        self, *transport_modes: TransportMode
    ) -> Quantity:
        modes = get_default_transport_modes_if_empty(transport_modes)
        carbon_emission_per_trip = self.get_carbon_emission(*modes)
        return carbon_emission_per_trip / self.nb_employees * yearly

    def get_teq_carbon_emission(self, *transport_modes: TransportMode) -> float:
        modes = get_default_transport_modes_if_empty(transport_modes)
        return round(float(self.get_carbon_emission(*modes) / (tonEC / yearly)), 1)

    def compute_required_parking_spots(self) -> Quantity:
        return self.count_employees_per_mode(TransportMode.CAR) * car_parking_spot

    def get_cities_with_most_employees(self, limit: int = 5) -> Dict[str, int]:
        cities: Dict[str, int] = defaultdict(int)
        for employee in self.scenario.get_origins():
            cities[employee.address_details.city] += 1
        ordered_cities = sorted(cities.items(), key=lambda x: x[1], reverse=True)
        return dict(ordered_cities[:limit])

    def get_sites_by_commune(self) -> Dict[str, List[str]]:
        communes: Dict[str, List[str]] = defaultdict(list)
        for site in self.get_sites():
            commune = site.address_details.city
            if commune in communes:
                communes[commune].append(site.nickname)
            else:
                communes[commune] = [site.nickname]
        return communes


def get_default_transport_modes_if_empty(
    transport_mode: Tuple[TransportMode, ...],
) -> Tuple[TransportMode, ...]:
    return transport_mode if len(transport_mode) > 0 else tuple(TransportMode)


def convert_carbon_emission_gec_per_commute_to_teq_per_year(
    carbon_emissions_gec: int,
) -> float:
    carbon_emissions_per_gec_year = carbon_emissions_gec * 2 * 5 * 47
    return round(carbon_emissions_per_gec_year / 1_000_000, 1)


def gather_alternative_arrival_time(
    scenario: ConsolidatedScenario,
) -> List[Tuple[int, int]]:
    times = None
    for commute in scenario.commutes:
        commute_alternate_times = set(commute.data.alternative_arrival_time.keys())
        if times is None:
            times = commute_alternate_times
        elif commute_alternate_times != times:
            raise ValueError(f"Non homogeneous alternate times in commutes {scenario}")
    if times is None or times == set():
        return []
    else:
        times.add((8, 30))
        return sorted(list(times), key=lambda t: t[0] * 60 + t[1])
