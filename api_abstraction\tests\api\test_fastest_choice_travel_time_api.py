from typing import Any

from api_abstraction.api.fastest_choice_travel_time_api import (
    FastestChoiceTravelTimeApi,
)
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


class TestFastestChoiceTravelTimeApi:
    def test_should_return_attributes_from_fastest_api(
        self, fake_travel_timer: Any
    ) -> None:
        api = FastestChoiceTravelTimeApi(
            [
                fake_travel_timer(984, 54, 68),
                fake_travel_timer(223, 9875, 543),
                fake_travel_timer(249, 0, 0),
            ]
        )

        attributes = api.time(
            GeoCoordinates(0.0, 1.0),
            GeoCoordinates(1.0, 0.0),
            TransportMode.CAR,
            (8, 30),
        )

        assert attributes.duration == 223
        assert attributes.distance == 9875
        assert attributes.emission == 543

    def test_should_return_attributes_from_fastest_api_if_apis_fail(
        self, fake_travel_timer: Any, failing_api: Any, inapt_api: Any
    ) -> None:
        api = FastestChoiceTravelTimeApi(
            [
                fake_travel_timer(984, 54, 68),
                fake_travel_timer(223, 9875, 543),
                inapt_api,
                failing_api,
            ]
        )

        attributes = api.time(
            GeoCoordinates(0.0, 1.0),
            GeoCoordinates(1.0, 0.0),
            TransportMode.CAR,
            (8, 30),
        )

        assert attributes.duration == 223
        assert attributes.distance == 9875
        assert attributes.emission == 543
