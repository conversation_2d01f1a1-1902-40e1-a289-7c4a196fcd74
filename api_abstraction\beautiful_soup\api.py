from typing import Callable, Dict, Optional, Tuple

import requests
from bs4 import BeautifulSoup
from regex import regex

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from api_abstraction.geopy.ban_france import BanFranceGeocoder
from mobility.ir.geo_study import Address, GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import meters
from mobility.workers.distance_computer import compute_distance


class BeautifulSoupTravelTimeAPI(TravelTimeApi):
    max_call_per_period = 1
    period_in_seconds = 1
    retries = 2
    retry_delay_in_seconds = 1

    def __init__(
        self,
        reporter: EventReporter,
        url_maker: Callable[
            [GeoCoordinates, GeoCoordinates, TransportMode, Tuple[int, int]], str
        ],
        soup_journey_parser: Callable[[BeautifulSoup], JourneyAttribute],
        post_data_maker: Optional[
            Callable[[Address, Address, TransportMode, Tuple[int, int]], Dict]
        ] = None,
    ) -> None:
        super().__init__("", reporter)
        self.url_maker = url_maker
        self.post_data_maker = post_data_maker
        self.soup_journey_parser = soup_journey_parser
        self._beautiful_soup_caller = self._call_bs
        self.ban_france = BanFranceGeocoder("", reporter)

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        url = self.url_maker(origin, destination, mode, arrival_time)
        if self.post_data_maker is None:
            post_data = None
        else:
            origin_address = self.ban_france.reverse(origin)
            destination_address = self.ban_france.reverse(destination)
            post_data = self.post_data_maker(
                origin_address, destination_address, mode, arrival_time
            )
        soup = self._beautiful_soup_caller(url, post_data)
        journey = self.soup_journey_parser(soup)
        if journey.distance is None:
            return JourneyAttribute(
                duration=journey.duration,
                emission=journey.emission,
                distance=int(compute_distance(origin, destination) * 1.4 / meters),
            )
        else:
            return journey

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        raise ApiInapt("BS API cannot compute isochrones")

    def _call_bs(self, url: str, post_data: Optional[Dict] = None) -> BeautifulSoup:
        if post_data is None:
            return BeautifulSoup(requests.get(url).text)
        else:
            return BeautifulSoup(requests.post(url, data=post_data).text)


def nmp_ihm_canaltp_url_maker(
    origin: GeoCoordinates,
    destination: GeoCoordinates,
    mode: TransportMode,
    arrival_time: Tuple[int, int],
) -> str:
    date = "19%2F01%2F2021"
    base_url = "https://nmp-ihm.ctp.prod.canaltp.fr/fr/load/c4SuSCbe/journey/result/?"
    params = [
        f"search[from][autocomplete]={origin.longitude}%3B{origin.latitude}",
        f"search[from][autocomplete-hidden]={origin.longitude}%3B{origin.latitude}",
        f"search[to][autocomplete]={destination.longitude}%3B{destination.latitude}",
        f"search[to][autocomplete-hidden]={destination.longitude}%3B{destination.latitude}",
        f"search[datetime][date]={date}",
        "search[datetime_represents]=arrival",
        "search[datetime][time][hour]=8",
        "search[datetime][time][minute]=30",
        "search[modes][]=physical_mode%3ABus",
        "search[modes][]=physical_mode%3ACoach",
        "search[modes][]=physical_mode%3AMetro%3Bphysical_mode%3ALocalTrain%3Bphysical_mode%3ARapidTransit%3Bphysical_mode%3ALongDistanceTrain%3Bphysical_mode%3ATrain",
        "search[count]=",
        "search[min_nb_journeys]=2",
        "search[max_nb_journeys]=4",
    ]
    return base_url + "&".join(params)


def parse_hour_min_text_as_duration_in_seconds(string: str) -> int:
    hour_minute = "(?P<hour>[0-9]+)\\s*h\\s*(?P<minute>[0-9]+)\\s*min"
    match = regex.search(hour_minute, string)
    if match is None:
        just_hour = "(?P<hour>[0-9]+)\\s*h"
        just_minute = "(?P<minute>[0-9]+)\\s*min"
        match = regex.search(f"{just_hour}|{just_minute}", string)
        if match is None:
            raise ApiFail("Soup timing did not match hour-minute pattern {string}")
        else:
            matches = match.groupdict()
    else:
        matches = match.groupdict()
    hour = int(matches["hour"]) if matches["hour"] is not None else 0
    minutes = int(matches["minute"]) if matches["minute"] is not None else 0
    if hour == 0 and minutes == 0:
        raise ApiFail("Failed to init non null timings from {string}")
    return hour * 3600 + minutes * 60


def nmp_ihm_canaltp_soup_journey_parser(soup: BeautifulSoup) -> JourneyAttribute:
    journeys = soup.find_all("li", {"class": "ctp-journey"})
    details = soup.find_all("div", {"class": "ctp-details"})
    if len(journeys) != len(details):
        raise ApiFail(
            f"Different number of journeys and details: {len(journeys)} != {len(details)}"
        )
    found_journeys = []
    for journey, detail in zip(journeys, details):
        string = journey.find_all(
            "div", {"class": "ctp-right-info-element ctp-duration-info"}
        )[1].text
        duration = parse_hour_min_text_as_duration_in_seconds(string)
        emission = int(detail.find("span", {"class": "ctp-value"}).text)
        path = detail.find("ol", {"class": "list-detail-iti"})
        nb_tc_stops = len(
            path.find_all("li", {"class": "ctp-section-public_transport"})
        )
        if nb_tc_stops > 0:
            found_journeys.append(
                JourneyAttribute(
                    duration=duration,
                    distance=None,
                    emission=emission,
                )
            )
    if len(found_journeys) == 0:
        raise ApiFail("Could not extract info from soup")
    else:
        return min(found_journeys, key=lambda j: j.duration)


def oura_soup_url_maker(
    origin: GeoCoordinates,
    destination: GeoCoordinates,
    mode: TransportMode,
    arrival_time: Tuple[int, int],
) -> str:
    return "https://www.oura.com/calculateur"


def oura_soup_post_data_maker(
    origin: Address,
    destination: Address,
    mode: TransportMode,
    arrival_time: Tuple[int, int],
) -> Dict:
    soup = BeautifulSoup(requests.get("https://www.oura.com/calculateur").text)
    if soup is None:
        raise ValueError("Could not parse oura/calculateur")
    form = soup.find(id="search-block-form")
    if form is None or isinstance(form, str):
        raise ValueError("Could not find form search-block-form")
    form_input = form.find("input", attrs={"name": "form_build_id"})
    if form_input is None or isinstance(form_input, str):
        raise ValueError("Could not find form input named form_build_id")
    form_build_id_value = form_input.get("value")
    hour, minute = arrival_time
    date = "31/05/2022"
    return {
        "via_txt": "",
        "via_time": "05",
        "algorithm": "FASTEST",
        "rabattement": "WALKTC",
        "maxwalkdist": "2000",
        "maxbikedist": "4",
        "maxroutedist": "10",
        "modes[TRAIN]": "TRAIN",
        "modes[METRO]": "METRO",
        "modes[TRAMWAY]": "TRAMWAY",
        "modes[BUS]": "BUS",
        "origine_txt": origin.normalized,
        "destination_txt": destination.normalized,
        "time_travel[date]": date,
        "time_travel_type": "arrival",
        "hour_travel": f"{hour:02}",
        "min_travel": f"{minute:02}",
        "origine_id": "",
        "origine_type": "",
        "origine_lat": "",
        "origine_lon": "",
        "via_id": "",
        "via_type": "",
        "via_lat": "",
        "via_lon": "",
        "destination_id": "",
        "destination_type": "",
        "destination_lat": "",
        "destination_lon": "",
        "op": "Rechercher",
        "form_build_id": form_build_id_value,
        "form_id": "oura_cityway_travel_form_page",
        "url": "",
    }


def oura_soup_journey_parser(soup: BeautifulSoup) -> JourneyAttribute:
    alert = soup.find(id="flashmessage", class_="alert alert-error")
    if alert is not None:
        raise ApiFail(f"Failed oura request:\n{alert}")
    found_journeys = []
    for overview_id in range(10):
        overview = soup.find(id=f"overview_{overview_id}")
        if overview is None or isinstance(overview, str):
            continue
        bold_text = overview.find_all("b")
        duration = parse_hour_min_text_as_duration_in_seconds(bold_text[1].text)
        found_journeys.append(
            JourneyAttribute(
                duration=duration,
                distance=None,
                emission=None,
            )
        )
    if len(found_journeys) == 0:
        raise ApiFail("Could not extract info from soup")
    else:
        return min(found_journeys, key=lambda j: j.duration)
