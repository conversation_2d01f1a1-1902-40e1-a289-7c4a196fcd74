import logging
from typing import Any, Dict, Tuple
from unittest.mock import Mock

import pytest

from api_abstraction.api.api import ApiF<PERSON>, ApiInapt, ApiTimeout
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.google.api import GoogleTravelTimeAPI, JourneyAttribute
from api_abstraction.google.base_google_api import GoogleRequestParameters
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import hours, minutes, seconds


@pytest.fixture
def mock_compute_route(geo_coordinates) -> Any:
    class fake_compute_route:
        def compute_route(
            self,
            parameters: GoogleRequestParameters,
        ) -> JourneyAttribute:
            if (
                parameters.origin == geo_coordinates(46.1146, 4.7031)
                and parameters.destination == geo_coordinates(45.7668, 4.8566)
                and parameters.mode == TransportMode.CAR
                and parameters.departure_time is not None
                and parameters.departure_time.weekday() == 1
                and parameters.traffic_model == "pessimistic"
            ):
                traffic_duration_by_hour = {
                    4: 55 * minutes,
                    5: 55 * minutes,
                    6: 55 * minutes,
                    7: 1 * hours,
                    8: 1 * hours + 15 * minutes,
                    9: 1 * hours + 5 * minutes,
                    10: 1 * hours,
                }
                """ Compute duration as a linear function between
                the stops giving in the above table. """
                req_hour = parameters.departure_time.hour
                d_h = traffic_duration_by_hour[req_hour]
                d_hp1 = traffic_duration_by_hour[req_hour + 1]
                delta_s = (
                    parameters.departure_time.second
                    + parameters.departure_time.minute * 60
                )
                traffic_duration = (delta_s * d_hp1 + (3600 - delta_s) * d_h) / 3600
                return JourneyAttribute(
                    duration=int(traffic_duration // seconds),
                    distance=55933,
                    emission=None,
                )
            elif (
                parameters.origin == geo_coordinates(46.1146, 4.7031)
                and parameters.destination == geo_coordinates(45.7668, 4.8566)
                and parameters.mode == TransportMode.BICYCLE
                and parameters.departure_time is None
                and parameters.arrival_time is not None
            ):
                return JourneyAttribute(duration=27630, distance=55933, emission=None)
            else:
                raise ApiFail("Wrong use of mock API")

    return fake_compute_route()


@pytest.fixture
def failing_traffic_compute_route() -> Any:
    class fake_compute_route:
        def __init__(self, n_failing_calls: int):
            self.n_calls = 0
            self.n_failing_calls = n_failing_calls

        def compute_route(
            self, parameters: GoogleRequestParameters
        ) -> JourneyAttribute:
            self.n_calls += 1
            if self.n_calls <= self.n_failing_calls:
                raise ApiTimeout("Duration in traffic not computed")
            else:
                return JourneyAttribute(duration=4212, distance=55933, emission=None)

    return fake_compute_route


class TestDistanceGoogleTravelTimeApi:
    def test_compute_route_compute_belleville_to_lyon_driving(self, mock_compute_route):

        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.google_routing_client = mock_compute_route
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=46.1146, longitude=4.7031)
        destination = GeoCoordinates(latitude=45.7668, longitude=4.8566)

        attributes = travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

        # expected departure ~ 7h30 => duration ~= 1h07
        assert 3600 + 5 * 60 <= attributes.duration <= 3600 + 10 * 60
        assert attributes.distance == 55933

    def test_compute_route_compute_belleville_to_lyon_driving_at_10(
        self, mock_compute_route
    ):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.google_routing_client = mock_compute_route
        origin = GeoCoordinates(latitude=46.1146, longitude=4.7031)
        destination = GeoCoordinates(latitude=45.7668, longitude=4.8566)

        attributes = travel_timer.time(origin, destination, TransportMode.CAR, (10, 0))

        # expected departure ~ 8h55 => duration ~= 1h05
        assert 3600 + 2 * 60 <= attributes.duration <= 3600 + 8 * 60
        assert attributes.distance == 55933

    def test_compute_route_fails_if_no_route(self, mock_compute_route):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.google_routing_client = mock_compute_route
        origin = GeoCoordinates(latitude=4.7031, longitude=46.1146)
        destination = GeoCoordinates(latitude=4.8566, longitude=45.7668)

        with pytest.raises(ApiFail):
            travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

    def test_compute_route_is_resilient_to_transient_traffic_failure(
        self, failing_traffic_compute_route
    ):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.google_routing_client = failing_traffic_compute_route(
            n_failing_calls=4
        )
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.retries = 5
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=4.7031, longitude=46.1146)
        destination = GeoCoordinates(latitude=4.8566, longitude=45.7668)

        attributes = travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

        assert attributes.duration == 4212

    def test_compute_route_times_out_on_repeated_traffic_failure(
        self, failing_traffic_compute_route
    ):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.google_routing_client = failing_traffic_compute_route(
            n_failing_calls=12
        )
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.retries = 2
        origin = GeoCoordinates(latitude=4.7031, longitude=46.1146)
        destination = GeoCoordinates(latitude=4.8566, longitude=45.7668)

        with pytest.raises(ApiTimeout):
            travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

    def test_raise_an_error_when_asking_to_compute_an_isochrone(self) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        destination = GeoCoordinates(45.0, 4.0)

        with pytest.raises(TypeError):
            travel_timer.compute_isochrone(
                Territory.LYON, destination, TransportMode.PUBLIC_TRANSPORT, 3600
            )

    @pytest.mark.parametrize(
        "car_like_mode",
        [
            TransportMode.CAR,
            TransportMode.MOTORCYCLE,
            TransportMode.CARPOOLING,
            TransportMode.ELECTRIC_CAR,
            TransportMode.ELECTRIC_MOTORCYCLE,
        ],
    )
    def test_get_time_from_store_for_car_like_modes(
        self, mock_compute_route, car_like_mode
    ):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.google_routing_client = mock_compute_route
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=0, longitude=1)
        destination = GeoCoordinates(latitude=1, longitude=0)
        travel_timer.car_journeys = {
            destination: {
                origin: {
                    8 * hours
                    + 29
                    * minutes: JourneyAttribute(
                        duration=100,
                        distance=90,
                        emission=None,
                    ),
                    8 * hours
                    + 31
                    * minutes: JourneyAttribute(
                        duration=120,
                        distance=100,
                        emission=None,
                    ),
                }
            }
        }

        attributes = travel_timer.time(origin, destination, car_like_mode, (8, 30))

        assert attributes.duration == 110
        assert attributes.distance == 95
        assert attributes.emission is None

    def test_time_biking(self, mock_compute_route):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.google_routing_client = mock_compute_route
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=46.1146, longitude=4.7031)
        destination = GeoCoordinates(latitude=45.7668, longitude=4.8566)

        attributes = travel_timer.time(
            origin, destination, TransportMode.BICYCLE, (8, 30)
        )

        assert attributes.duration == 27630
        assert attributes.distance == 55933

    def test_fails_to_time_electric_bike(self, mock_compute_route):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.google_routing_client = mock_compute_route
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=46.1146, longitude=4.7031)
        destination = GeoCoordinates(latitude=45.7668, longitude=4.8566)

        with pytest.raises(ApiInapt):
            travel_timer.time(
                origin, destination, TransportMode.ELECTRIC_BICYCLE, (8, 30)
            )


class TestDistanceGoogleTravelTimeInternals:
    def test_can_record_car_travel(self):
        dm = GoogleTravelTimeAPI("AIza fake key", EventReporter())

        dm._record_car_journey(
            GeoCoordinates(0, 1),
            GeoCoordinates(1, 0),
            8 * hours,
            JourneyAttribute(duration=1000, distance=1000, emission=1000),
        )

        assert len(dm.car_journeys) == 1

    def test_can_estimate_car_duration(self):
        dm = GoogleTravelTimeAPI("AIza fake key", EventReporter())

        duration = dm._compute_expected_car_duration(
            GeoCoordinates(0, 1), GeoCoordinates(1, 0), (8, 0)
        )

        assert duration > 0

    def test_can_estimate_car_duration_based_on_records(self):
        dm = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        o = GeoCoordinates(0, 1)
        d = GeoCoordinates(1, 0)
        dm._record_car_journey(
            o,
            d,
            8 * hours,
            JourneyAttribute(duration=1000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            9 * hours,
            JourneyAttribute(duration=2000, distance=1000, emission=1000),
        )

        duration = dm._compute_expected_car_duration(o, d, (8, 30))

        assert duration == 1500 * seconds

    def test_can_estimate_car_duration_based_on_records_uneven(self):
        dm = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        o = GeoCoordinates(0, 1)
        d = GeoCoordinates(1, 0)
        dm._record_car_journey(
            o,
            d,
            8 * hours,
            JourneyAttribute(duration=1000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            9 * hours,
            JourneyAttribute(duration=2000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            10 * hours,
            JourneyAttribute(duration=800, distance=1000, emission=1000),
        )

        duration = dm._compute_expected_car_duration(o, d, (9, 10))

        assert duration == 1800 * seconds

    def test_can_retrieve_stored_car_result_in_tolerance(self):
        dm = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        o = GeoCoordinates(0, 1)
        d = GeoCoordinates(1, 0)
        dm._record_car_journey(
            o,
            d,
            8 * hours,
            JourneyAttribute(duration=1000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            8 * hours + 56 * minutes,
            JourneyAttribute(duration=2000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            9 * hours + 3 * minutes,
            JourneyAttribute(duration=2100, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            10 * hours,
            JourneyAttribute(duration=800, distance=1000, emission=1000),
        )

        attributes = dm._get_stored_result_closest_to_arrival(
            o, d, TransportMode.CAR, (9, 0), 5 * minutes
        )

        assert attributes == JourneyAttribute(
            duration=2100, distance=1000, emission=1000
        )

    def test_cannot_retrieve_stored_car_result_out_of_tolerance(self):
        dm = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        o = GeoCoordinates(0, 1)
        d = GeoCoordinates(1, 0)
        dm._record_car_journey(
            o,
            d,
            8 * hours,
            JourneyAttribute(duration=1000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            8 * hours + 55 * minutes,
            JourneyAttribute(duration=2000, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            9 * hours + 5 * minutes,
            JourneyAttribute(duration=2100, distance=1000, emission=1000),
        )
        dm._record_car_journey(
            o,
            d,
            10 * hours,
            JourneyAttribute(duration=800, distance=1000, emission=1000),
        )

        duration = dm._get_stored_result_closest_to_arrival(
            o, d, TransportMode.CAR, (9, 0), 5 * minutes
        )

        assert duration is None

    def test_should_not_find_different_results_for_same_od(self) -> None:
        api = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        mock_google = Mock()
        mock_google.compute_route.side_effect = [
            JourneyAttribute(duration=1878, distance=27492, emission=None),
            JourneyAttribute(duration=2341, distance=27492, emission=None),
            JourneyAttribute(duration=2260, distance=27492, emission=None),
        ]
        api.google_routing_client = mock_google
        o = GeoCoordinates(45.47279, 4.76736)
        d = GeoCoordinates(45.61949, 4.70508)
        first_result = api._time(o, d, TransportMode.CAR, (6, 30))

        second_result = api._time(o, d, TransportMode.CARPOOLING, (6, 30))

        assert first_result == second_result

    def test_should_not_retry_finding_departure_time_for_same_od(self) -> None:
        api = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        mock_google = Mock()
        # The folowing set of journeys is designed to make the departure time
        # algorithm fail : after 4 iterations it should not have an inf
        # and sup travel time that makes an arrival at 8h30, and it should
        # default to using one of the values in the set (the one that arrives
        # the closest to 8h30)
        # Then, when requesting travel time for carpooling, the algorithm
        # should NOT retry to search for an inf/sup journey, but it should
        # default to the same value as was chosen for the car journey, because
        # we do not want discrepancies in the travel times between car and other
        # car-like modes.
        mock_google.compute_route.side_effect = [
            JourneyAttribute(duration=3600 + 40 * 60, distance=27492, emission=None),
            JourneyAttribute(duration=3600 + 35 * 60, distance=27492, emission=None),
            JourneyAttribute(duration=3600 + 25 * 60, distance=27492, emission=None),
            JourneyAttribute(duration=3600 + 15 * 60, distance=27492, emission=None),
        ]
        api.google_routing_client = mock_google
        o = GeoCoordinates(45.47279, 4.76736)
        d = GeoCoordinates(45.61949, 4.70508)
        first_result = api._time(o, d, TransportMode.CAR, (8, 30))

        second_result = api._time(o, d, TransportMode.CARPOOLING, (8, 30))

        assert first_result == second_result
        assert first_result.duration == 3600 + 40 * 60


@pytest.fixture
def mock_compute_route_linear_with_gap() -> Any:
    class fake_compute_route:
        def compute_route(
            self, parameters: GoogleRequestParameters
        ) -> JourneyAttribute:
            if parameters.departure_time is not None:
                if (
                    parameters.departure_time.hour <= 6
                    and parameters.departure_time.minute <= 21
                ):
                    duration_minutes = 60 + 43
                else:
                    duration_minutes = 60 * 2 + 15
                return JourneyAttribute(
                    duration=duration_minutes * 60, distance=55933, emission=None
                )
            else:
                raise ValueError("Wrong api call")

    return fake_compute_route()


def mock_compute_route_parametrized(
    mock_parameters: Dict[Tuple[int, int], Tuple[int, int, int]],
) -> Any:
    class fake_compute_route:
        def compute_route(
            self, parameters: GoogleRequestParameters
        ) -> JourneyAttribute:
            if parameters.departure_time is not None:
                departure = (
                    parameters.departure_time.hour,
                    parameters.departure_time.minute,
                )
                duration_in_traffic, distance, _ = mock_parameters[departure]
                return JourneyAttribute(
                    duration=duration_in_traffic, distance=distance, emission=None
                )
            else:
                raise ValueError("Wrong api call")

    return fake_compute_route()


class TestFindDurationWithLinearRegression:
    def test_should_find_duration_with_lin_reg(
        self, mock_compute_route_linear_with_gap
    ):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        travel_timer.google_routing_client = mock_compute_route_linear_with_gap
        origin = GeoCoordinates(latitude=46.1146, longitude=4.7031)
        destination = GeoCoordinates(latitude=45.7668, longitude=4.8566)

        attributes = travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

        assert 7400 <= attributes.duration <= 7800
        assert attributes.distance == 55933

    def test_should_find_duration_on_real_case_actually(self) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        parameters = {
            (4, 33): (3277, 53886, 0),
            (7, 35): (5231, 57274, 0),
            (7, 7): (6898, 53886, 0),
            (6, 44): (6708, 53886, 0),
        }
        travel_timer.google_routing_client = mock_compute_route_parametrized(parameters)
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=49.84288, longitude=5.7223)
        destination = GeoCoordinates(latitude=49.62049, longitude=6.14654)

        attributes = travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

        assert attributes.duration == 6597
        assert attributes.distance == 53886

    def test_should_find_duration_when_lin_reg_is_unavailable(self):
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        travel_timer.retry_delay_in_seconds = 0
        parameters = {(8, 10): (1929, 5297, 0), (7, 57): (1969, 5297, 0)}
        travel_timer.google_routing_client = mock_compute_route_parametrized(parameters)
        travel_timer.logger.setLevel(logging.DEBUG)
        origin = GeoCoordinates(latitude=45.752316, longitude=3.093546)
        destination = GeoCoordinates(latitude=45.782245, longitude=3.094608)

        attributes = travel_timer.time(origin, destination, TransportMode.CAR, (8, 30))

        assert attributes.duration == 1969
        assert attributes.distance == 5297


class TestComputeInfSupJourney:
    def test_should_return_non_if_no_journey_is_stored(self) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        origin = GeoCoordinates(45.5, 5.0)
        destination = GeoCoordinates(46.5, 5.0)
        arrival_time = 9 * hours

        inf, sup = travel_timer._compute_inf_sup_journeys(
            origin, destination, arrival_time
        )

        assert inf is None
        assert sup is None

    def test_should_return_the_two_journeys_arriving_around_requested_time(
        self, journey_attribute: Any
    ) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        origin = GeoCoordinates(45.5, 5.0)
        destination = GeoCoordinates(46.5, 5.0)
        arrival_time = 9 * hours
        journey_8 = journey_attribute(duration=12345)
        travel_timer._record_car_journey(origin, destination, 8 * hours, journey_8)
        journey_10 = journey_attribute(duration=45612)
        travel_timer._record_car_journey(origin, destination, 10 * hours, journey_10)

        inf, sup = travel_timer._compute_inf_sup_journeys(
            origin, destination, arrival_time
        )

        assert inf == (8 * hours, journey_8)
        assert sup == (10 * hours, journey_10)

    def test_should_return_the_sup_journey_above_requested_time(
        self, journey_attribute: Any
    ) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        origin = GeoCoordinates(45.5, 5.0)
        destination = GeoCoordinates(46.5, 5.0)
        arrival_time = 9 * hours
        journey_10 = journey_attribute(duration=45612)
        travel_timer._record_car_journey(origin, destination, 10 * hours, journey_10)

        inf, sup = travel_timer._compute_inf_sup_journeys(
            origin, destination, arrival_time
        )

        assert inf is None
        assert sup == (10 * hours, journey_10)

    def test_should_return_the_inf_journey_before_requested_time(
        self, journey_attribute: Any
    ) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        origin = GeoCoordinates(45.5, 5.0)
        destination = GeoCoordinates(46.5, 5.0)
        arrival_time = 9 * hours
        journey_8 = journey_attribute(duration=12345)
        travel_timer._record_car_journey(origin, destination, 8 * hours, journey_8)

        inf, sup = travel_timer._compute_inf_sup_journeys(
            origin, destination, arrival_time
        )

        assert inf == (8 * hours, journey_8)
        assert sup is None

    def test_should_return_the_two_journeys_arriving_around_requested_time_after_filtering(
        self, journey_attribute: Any
    ) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        origin = GeoCoordinates(45.5, 5.0)
        destination = GeoCoordinates(46.5, 5.0)
        arrival_time = 9 * hours
        journey = journey_attribute()
        travel_timer._record_car_journey(origin, destination, 8 * hours, journey)
        travel_timer._record_car_journey(origin, destination, 8.5 * hours, journey)
        travel_timer._record_car_journey(origin, destination, 9.5 * hours, journey)
        travel_timer._record_car_journey(origin, destination, 10 * hours, journey)

        inf, sup = travel_timer._compute_inf_sup_journeys(
            origin, destination, arrival_time
        )

        assert inf == (8.5 * hours, journey)
        assert sup == (9.5 * hours, journey)

    def test_should_return_the_two_journeys_departing_successively_and_arriving_around_requested_time(
        self, journey_attribute: Any
    ) -> None:
        travel_timer = GoogleTravelTimeAPI("AIza fake key", EventReporter())
        origin = GeoCoordinates(45.5, 5.0)
        destination = GeoCoordinates(46.5, 5.0)
        arrival_time = 8 * hours + 30 * minutes
        journey_4h33 = journey_attribute(duration=3277)
        journey_7h35 = journey_attribute(duration=5231)
        journey_7h07 = journey_attribute(duration=6898)
        travel_timer._record_car_journey(
            origin, destination, 5 * hours + 28 * minutes + 36 * seconds, journey_4h33
        )
        travel_timer._record_car_journey(
            origin, destination, 9 * hours + 2 * minutes + 34 * seconds, journey_7h35
        )
        travel_timer._record_car_journey(
            origin, destination, 9 * hours + 2 * minutes + 45 * seconds, journey_7h07
        )

        inf, sup = travel_timer._compute_inf_sup_journeys(
            origin, destination, arrival_time
        )

        assert inf == (5 * hours + 28 * minutes + 36 * seconds, journey_4h33)
        assert sup == (9 * hours + 2 * minutes + 45 * seconds, journey_7h07)
