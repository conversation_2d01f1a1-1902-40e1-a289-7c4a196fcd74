from typing import Optional, Tuple

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import ApiFail, JourneyAttribute, TravelTimeApi
from api_abstraction.trivial.trivial_travel_time import DistanceTravelTimer
from mobility.builders.airport_database import AirportDatabaseBuilder
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode
from mobility.quantity import kilometers
from mobility.workers.distance_computer import compute_distance
from mobility.workers.emission_computer import compute_mode_emission_from_distance


class FlightComputerApi(TravelTimeApi):
    def __init__(
        self,
        token: str,
        reporter: EventReporter,
        airports_csv: str,
        ground_timer: Optional[TravelTimeApi] = None,
    ) -> None:
        self.airport_database = AirportDatabaseBuilder().build(airports_csv)
        self.ground_timer = ground_timer or DistanceTravelTimer("", reporter)
        super().__init__(token, reporter)

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: str,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        airports = self._get_nearest_airports(origin, destination)
        if not airports:
            raise ApiFail("No airport found")
        origin_airport, destination_airport = airports

        flight_journey = self._calculate_flight_journey(
            origin_airport.coordinates, destination_airport.coordinates
        )

        try:
            access_journey, access_mode = self._get_best_ground_journey(
                origin, origin_airport.coordinates, arrival_time
            )
            egress_journey, egress_mode = self._get_best_ground_journey(
                destination_airport.coordinates, destination, arrival_time
            )

            return self._combine_journey_segments(
                flight_journey, access_journey, access_mode, egress_journey, egress_mode
            )
        except ApiFail:
            return flight_journey

    def _get_nearest_airports(
        self, origin: GeoCoordinates, destination: GeoCoordinates
    ) -> Optional[Tuple]:
        origin_airport = self.airport_database.find_nearest_airport(origin)
        destination_airport = self.airport_database.find_nearest_airport(destination)
        if origin_airport and destination_airport:
            return (origin_airport, destination_airport)
        return None

    def _calculate_flight_journey(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
    ) -> JourneyAttribute:
        FLIGHT_SPEED = 800
        FLIGHT_EMISSION_RATE = 230
        BASE_FLIGHT_TIME = 2 * 3600

        distance_km = float(compute_distance(origin, destination) / kilometers)
        duration = int(3600 * distance_km / FLIGHT_SPEED) + BASE_FLIGHT_TIME
        distance = int(distance_km * 1000)
        emission = int(distance_km * FLIGHT_EMISSION_RATE)

        return JourneyAttribute(
            duration=duration,
            distance=distance,
            emission=emission,
        )

    def _get_best_ground_journey(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        arrival_time: Tuple[int, int],
    ) -> Tuple[JourneyAttribute, TransportMode]:
        car_journey = self.ground_timer._time(
            origin, destination, TransportMode.CAR, arrival_time
        )
        pt_journey = self.ground_timer._time(
            origin, destination, TransportMode.PUBLIC_TRANSPORT, arrival_time
        )
        if pt_journey.duration <= car_journey.duration:
            best_journey = pt_journey
            best_mode = TransportMode.PUBLIC_TRANSPORT
        else:
            best_journey = car_journey
            best_mode = TransportMode.CAR
        return best_journey, best_mode

    def _combine_journey_segments(
        self,
        flight: JourneyAttribute,
        access_journey: JourneyAttribute,
        access_mode: TransportMode,
        egress_journey: JourneyAttribute,
        egress_mode: TransportMode,
    ) -> JourneyAttribute:

        total_distance = 0
        if flight.distance is not None:
            total_distance += flight.distance
        if access_journey.distance is not None:
            total_distance += access_journey.distance
        if egress_journey.distance is not None:
            total_distance += egress_journey.distance

        total_emission = 0
        if flight.emission is not None:
            total_emission += flight.emission

        if access_journey.emission is not None:
            total_emission += access_journey.emission
        elif access_journey.distance is not None:
            total_emission += compute_mode_emission_from_distance(
                access_mode, access_journey.distance
            )
        if egress_journey.emission is not None:
            total_emission += egress_journey.emission
        elif egress_journey.distance is not None:
            total_emission += compute_mode_emission_from_distance(
                egress_mode, egress_journey.distance
            )

        return JourneyAttribute(
            duration=flight.duration
            + access_journey.duration
            + egress_journey.duration,
            distance=total_distance,
            emission=total_emission,
        )
