import geopy
import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import ApiFail, ApiTimeout
from api_abstraction.geopy.ban_france import BanFranceGeocoder
from mobility.ir.geo_study import Address, GeoCoordinates


@pytest.fixture
def mock_banfrance():
    class fake_banfrance:
        def geocode(self, address: str) -> geopy.location.Location:
            if address == "failure of the api 34984748 complete fail":
                return None
            elif address == "38 boulevard des brotteaux":
                return geopy.location.Location(
                    address="38 Boulevard des Brotteaux 69006 Lyon",
                    point=geopy.point.Point(
                        latitude=45.766885,
                        longitude=4.856652,
                        altitude=0.0,
                    ),
                    raw={
                        "type": "Feature",
                        "geometry": {
                            "type": "Point",
                            "coordinates": [4.856652, 45.766885],
                        },
                        "properties": {
                            "label": "38 Boulevard des Brotteaux 69006 Lyon",
                            "score": 0.881470627337148,
                            "housenumber": "38",
                            "id": "69386_1130_00038",
                            "type": "housenumber",
                            "name": "38 Boulevard des Brotteaux",
                            "postcode": "69006",
                            "citycode": "69386",
                            "x": 844287.14,
                            "y": 6520282.77,
                            "city": "Lyon",
                            "district": "Lyon 6e Arrondissement",
                            "context": "69, Rhône, Auvergne-Rhône-Alpes",
                            "importance": 0.6961769007086287,
                            "street": "Boulevard des Brotteaux",
                        },
                    },
                )
            else:
                raise geopy.exc.GeocoderTimedOut("timeout")

        def reverse(self, query):
            if query == (46.0, 4.0):
                return geopy.location.Location(
                    address="1671 Route de Villemontais 42155 Ouches",
                    point=geopy.point.Point(
                        latitude=45.999789,
                        longitude=4.000422,
                        altitude=0.0,
                    ),
                    raw={
                        "type": "Feature",
                        "geometry": {
                            "type": "Point",
                            "coordinates": [4.000422, 45.999789],
                        },
                        "properties": {
                            "label": "1671 Route de Villemontais 42155 Ouches",
                            "score": 0.9999993556116189,
                            "housenumber": "1671",
                            "id": "42162_0007_01671",
                            "type": "housenumber",
                            "x": 777423.83,
                            "y": 6544940.89,
                            "importance": 0.33283632620891956,
                            "name": "1671 Route de Villemontais",
                            "postcode": "42155",
                            "citycode": "42162",
                            "city": "Ouches",
                            "context": "42, Loire, Auvergne-Rhône-Alpes",
                            "street": "Route de Villemontais",
                            "distance": 40,
                        },
                    },
                )
            elif query == (0.0, 0.0):
                raise geopy.exc.GeocoderTimedOut("timeout")
            else:
                return None

    return fake_banfrance()


@pytest.fixture
def mock_api_adresse_csv():
    def _mock(*args, **kwargs) -> str:
        return '\ufefflat,lon,result_latitude,result_longitude,result_label,result_distance,result_type,result_id,result_housenumber,result_name,result_street,result_postcode,result_city,result_context,result_citycode,result_oldcitycode,result_oldcity,result_district\r\n45.5,4.0,45.50016,3.998257,5000 Les Chambons 42560 Saint-Jean-Soleymieux,136,housenumber,42240_el1oft_05000,5000,Les Chambons,,42560,Saint-Jean-Soleymieux,"42, Loire, Auvergne-Rhône-Alpes",42240,,,\r\n45.6,6.0,,,,,,,,,,,,,,,,\r\n'

    return _mock


class TestBanFranceGeocoder:
    def test_geocoder_returns_geocoordinates(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        geoloc = geocoder.geocode("38 boulevard des brotteaux")

        assert isinstance(geoloc, GeoCoordinates)

    def test_geocoder_geocodes_an_address(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        geoloc = geocoder.geocode("38 boulevard des brotteaux")

        assert geoloc.latitude == 45.766885 and geoloc.longitude == 4.856652

    def test_geocoder_timeouts_with_api_timeout(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance
        geocoder.retry_delay_in_seconds = 0

        with pytest.raises(ApiTimeout) as e:
            geocoder.geocode("something something")
        assert e.value.message == "timeout"

    def test_geocoder_fails_on_undefined_address(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        with pytest.raises(ApiFail) as e:
            geocoder.geocode("failure of the api 34984748 complete fail")
        assert (
            e.value.message == "Cannot find geolocalization of failure of the "
            "api 34984748 complete fail"
        )

    def test_reverse_returns_address(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        address = geocoder.reverse(GeoCoordinates(latitude=46.0, longitude=4.0))

        assert isinstance(address, Address)

    def test_reverse_geocodes_coordinates(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        address = geocoder.reverse(GeoCoordinates(latitude=46.0, longitude=4.0))

        assert address == Address(
            full="1671 Route de Villemontais 42155 Ouches",
            normalized="1671 Route de Villemontais 42155 Ouches",
            city="Ouches",
            postcode="42155",
            citycode="42162",
            coordinates=GeoCoordinates(latitude=45.999789, longitude=4.000422),
        )

    def test_reverse_timeouts_with_api_timeout(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance
        geocoder.retry_delay_in_seconds = 0

        with pytest.raises(ApiTimeout) as e:
            geocoder.reverse(GeoCoordinates(latitude=0.0, longitude=0.0))
        assert e.value.message == "timeout"

    def test_reverse_fails_on_undefined_address(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        with pytest.raises(ApiFail) as e:
            geocoder.reverse(GeoCoordinates(latitude=10.0, longitude=0.0))
        assert e.value.message == "Cannot reverse geolocalization 10.0 0.0"

    def test_should_encode_all_the_details_from_address(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        precise_address = geocoder.geocode_details("38 boulevard des brotteaux")

        assert precise_address == Address(
            full="38 boulevard des brotteaux",
            normalized="38 Boulevard des Brotteaux 69006 Lyon",
            city="Lyon",
            postcode="69006",
            citycode="69386",
            coordinates=GeoCoordinates(latitude=45.766885, longitude=4.856652),
        )

    def test_geocode_details_timeouts_with_api_timeout(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance
        geocoder.retry_delay_in_seconds = 0

        with pytest.raises(ApiTimeout) as e:
            geocoder.geocode_details("wtf is this address timeouting my face")
        assert e.value.message == "timeout"

    def test_geocode_details_fails_on_undefined_address(self, mock_banfrance):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._geocoder = mock_banfrance

        with pytest.raises(ApiFail) as e:
            geocoder.geocode_details("failure of the api 34984748 complete fail")
        assert (
            e.value.message == "Cannot find geolocalization of failure of the "
            "api 34984748 complete fail"
        )

    def test_should_reverse_geocode_bulk_data(self, mock_api_adresse_csv):
        geocoder = BanFranceGeocoder("", EventReporter())
        geocoder._request_api_adresse_csv = mock_api_adresse_csv

        addresses = geocoder.bulk_reverse(
            [
                GeoCoordinates(latitude=46.0, longitude=4.0),
                GeoCoordinates(latitude=46.0, longitude=5.0),
            ]
        )

        assert isinstance(addresses, list)
        assert all(isinstance(a, Address) for a in addresses)
