from collections import defaultdict
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple


class Transaction:
    def __str__(self) -> str:
        if self.response_time and self.query_time:
            delta = f"in {self.response_time - self.query_time}"
        else:
            delta = "pending…"
        return (
            f"{self.response_error or 'Success'} {delta}\n"
            f"{self.query_string} => {self.response}"
        )

    def __init__(self) -> None:
        self.query_string: Optional[str] = None
        self.query_time: Optional[datetime] = None
        self.is_success: bool = False
        self.is_ended: bool = False
        self.response: Optional[str] = None
        self.response_time: Optional[datetime] = None
        self.response_error: Optional[Exception] = None

    def start(self, query_string: str) -> None:
        self.query_string = query_string
        self.query_time = datetime.now()

    def succeed(self, response: str) -> None:
        self.is_success = True
        self.is_ended = True
        self.response = response
        self.response_time = datetime.now()

    def fail(self, error: Exception) -> None:
        self.is_success = False
        self.is_ended = True
        self.response_error = error
        self.response_time = datetime.now()


class Burst:
    def __init__(self, api_class: str) -> None:
        self.transactions: List[Transaction] = list()
        self.api_class = api_class

    def new_transaction(self) -> Transaction:
        transaction = Transaction()
        self.transactions.append(transaction)
        return transaction

    def report_all(self) -> List[str]:
        events: List[str] = []
        for transaction in self.transactions:
            events.append(str(transaction))
        return events

    @property
    def title(self) -> str:
        return f"{self.api_class}:  {self.transactions[0].query_string}"

    @property
    def nb_failure(self) -> int:
        nb_failure = 0
        for transaction in self.transactions:
            if not transaction.is_success:
                nb_failure += 1
        return nb_failure

    @property
    def nb_success(self) -> int:
        nb_success = 0
        for transaction in self.transactions:
            if transaction.is_success:
                nb_success += 1
        return nb_success

    @property
    def finished_transactions(self) -> List[Transaction]:
        return [t for t in self.transactions if t.is_ended and t.response_time]

    def compute_total_duration(self) -> timedelta:
        query_times = [
            t.query_time for t in self.transactions if t.query_time is not None
        ]
        first_query_time = min(query_times)
        response_times = [
            t.response_time for t in self.transactions if t.response_time is not None
        ]
        last_response_time = max(response_times)
        return last_response_time - first_query_time

    def compute_nb_error(self) -> Tuple[int, int]:
        return self.nb_success, self.nb_failure

    def compile_errors(self) -> List[Exception]:
        errors: List[Exception] = list()
        for transaction in self.transactions:
            if transaction.response_error and transaction.response_error not in errors:
                errors.append(transaction.response_error)
        return errors


class EventReporter:
    def __init__(self) -> None:
        self.bursts: List[Burst] = list()
        self._transactions_by_api: Optional[Dict[str, List[Transaction]]] = None

    def new_burst(self, api_class: str) -> Burst:
        burst = Burst(api_class)
        self.bursts.append(burst)
        self._transactions_by_api = None
        return burst

    def report_all(self) -> List[List[str]]:
        events: List[List[str]] = []
        for burst in self.bursts:
            events.append(burst.report_all())
        return events

    @property
    def transactions_by_api(self) -> Dict[str, List[Transaction]]:
        if self._transactions_by_api is None:
            self._transactions_by_api = defaultdict(list)
            for b in self.bursts:
                self._transactions_by_api[b.api_class].extend(b.finished_transactions)
        return self._transactions_by_api

    @property
    def bursts_with_errors(self) -> List[Burst]:
        return [burst for burst in self.bursts if burst.nb_failure > 0]

    def report(self) -> List[str]:
        report: List[str] = ["", "###       REPORT        ###", ""]
        interval = timedelta(seconds=1)
        for burst in self.bursts_with_errors:
            report.append(burst.title)
            report.append(self.format_burst_error(*burst.compute_nb_error()))
            if burst.nb_success > 0:
                report.append(
                    self.format_burst_duration(burst.compute_total_duration())
                )
            else:
                report.append(self.format_errors(burst.compile_errors()))
            failure_strain = self.compute_strain(
                burst.transactions[0], burst.api_class, interval
            )
            success_strain = 0.0
            if burst.nb_success > 0:
                success_strain = self.compute_strain(
                    burst.transactions[-1], burst.api_class, interval
                )
            report.append(
                self.format_burst_strain(success_strain, failure_strain, interval)
            )
            report.append("----   ----    ----    ----")
        report.extend(self.format_nb_call_by_api(self.compute_nb_call_by_api()))
        return report

    def get_response_in(
        self, time_min: datetime, time_max: datetime, api_class: str
    ) -> List[Transaction]:
        return [
            t
            for t in self.transactions_by_api[api_class]
            if t.response_time is not None and time_min < t.response_time <= time_max
        ]

    def get_query_in(
        self, time_min: datetime, time_max: datetime, api_class: str
    ) -> List[Transaction]:
        return [
            t
            for t in self.transactions_by_api[api_class]
            if t.query_time is not None and time_min < t.query_time <= time_max
        ]

    def compute_strain(
        self,
        transaction: Transaction,
        api_class: str,
        delta_time: timedelta = timedelta(seconds=1),
    ) -> float:
        if transaction.query_time is None or transaction.response_time is None:
            return 0.0
        if transaction.query_time < transaction.response_time - delta_time:
            return 0.0
        time_min = transaction.response_time - delta_time
        time_max = transaction.response_time
        n_finished_before = len(self.get_response_in(time_min, time_max, api_class))
        n_started_before = len(self.get_query_in(time_min, time_max, api_class))
        return (n_started_before + n_finished_before) / 2.0 - 1.0

    def compute_nb_call_by_api(self) -> List[Tuple[str, int, int]]:
        calls_by_api = list()
        for api_name, transactions in self.transactions_by_api.items():
            nb_success = len(
                [transaction for transaction in transactions if transaction.is_success]
            )
            nb_error = len(
                [
                    transaction
                    for transaction in transactions
                    if not transaction.is_success
                ]
            )
            calls_by_api.append((api_name, nb_success, nb_error))
        return calls_by_api

    @staticmethod
    def format_nb_call_by_api(calls_by_api: List[Tuple[str, int, int]]) -> List[str]:
        return [
            f"{api_name}: {nb_success} success calls, {nb_error} error calls"
            for api_name, nb_success, nb_error in calls_by_api
        ]

    @staticmethod
    def format_burst_strain(
        success_strain: float,
        failure_strain: float,
        interval: timedelta,
    ) -> str:
        result = (
            f"Failure when strain > {failure_strain} / {interval.total_seconds() } s"
        )
        if success_strain:
            result += (
                f" success when strain < "
                f"{success_strain} / {interval.total_seconds()} s"
            )
        return result

    @staticmethod
    def format_burst_error(nb_success: int, nb_failure: int) -> str:
        return f"{nb_failure} failures" f", {nb_success} success"

    @staticmethod
    def format_burst_duration(time: timedelta) -> str:
        return f"{time.total_seconds()} s before completion"

    @staticmethod
    def format_errors(error_list: List[Exception]) -> str:
        return "\n".join(
            str(error) if str(error) else repr(error) for error in error_list
        )
