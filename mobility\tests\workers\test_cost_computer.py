from typing import Any
from unittest import mock

import pytest

from mobility.funky import ImmutableDict
from mobility.ir.cost import Cost, Cost<PERSON><PERSON>, CostPayer, Costs
from mobility.ir.transport import TransportMode
from mobility.quantity import adimensional, euros, trip, yearly
from mobility.workers.cost_computer import CostComputer
from mobility.workers.infrastructure_cost_calculator import (
    BICYCLE_PARKING_YEARLY_COST,
    CAR_PARKING_COST,
)


@pytest.fixture
def cost_computer():
    return CostComputer()


class TestCarParkingCommuteCostComputation:
    def test_should_compute_car_parking_commute_costs_with_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            car_parking_cost=500.0,
        )

        costs = cost_computer._compute_car_parking_commute_costs(site)

        employee_fee = 500.0 * euros / trip
        expected_costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=CAR_PARKING_COST,
            ),
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.EMPLOYEE,
                amount=employee_fee,
            ),
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=-employee_fee,
            ),
        ]
        assert costs == expected_costs

    def test_should_compute_car_parking_commute_costs_without_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            car_parking_cost=None,
        )
        costs = cost_computer._compute_car_parking_commute_costs(site)

        expected_costs = [
            Cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=CAR_PARKING_COST,
            )
        ]
        assert costs == expected_costs

    def test_should_compute_no_car_parking_commute_costs_for_coworking_site(
        self, coworking_site: Any, cost_computer: CostComputer
    ) -> None:
        site = coworking_site()
        costs = cost_computer._compute_car_parking_commute_costs(site)

        assert costs == []


class TestBicycleParkingCommuteCostComputation:
    def test_should_compute_bicycle_parking_commute_costs_with_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            bicycle_parking_cost=500.0,
        )

        costs = cost_computer._compute_bicycle_parking_commute_costs(site)

        employee_fee = 500.0 * euros / trip
        expected_costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=BICYCLE_PARKING_YEARLY_COST,
            ),
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.EMPLOYEE,
                amount=employee_fee,
            ),
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=-employee_fee,
            ),
        ]
        assert costs == expected_costs

    def test_should_compute_bicycle_parking_commute_costs_without_employee_fee(
        self, geo_site_factory: Any, cost_computer: CostComputer
    ) -> None:
        site = geo_site_factory(
            bicycle_parking_cost=None,
        )
        costs = cost_computer._compute_bicycle_parking_commute_costs(site)

        expected_costs = [
            Cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=BICYCLE_PARKING_YEARLY_COST,
            )
        ]
        assert costs == expected_costs

    def test_should_compute_no_bicycle_parking_commute_costs_for_coworking_site(
        self, coworking_site: Any, cost_computer: CostComputer
    ) -> None:
        site = coworking_site()
        costs = cost_computer._compute_bicycle_parking_commute_costs(site)

        assert costs == []


def assert_costs(costs: Costs, expected_costs: Costs):
    assert len(costs) == len(expected_costs), f"{costs} != {expected_costs}"
    for expected_cost in expected_costs:
        for actual_cost in costs:
            if (
                actual_cost.kind == expected_cost.kind
                and actual_cost.payer == expected_cost.payer
            ):
                print(expected_cost.kind)
                print(actual_cost.amount)
                print(expected_cost.amount)
                assert (
                    expected_cost.amount - 1 * euros / yearly
                    <= actual_cost.amount
                    <= expected_cost.amount + 1 * euros / yearly
                )
                break
        else:
            assert False, f"Missing expected cost {expected_cost}"


class TestCostComputation:
    def test_should_compute_no_costs_from_empty_commutes(
        self, cost_computer, individual_costs
    ):
        commutes = []

        costs = cost_computer.compute_commutes_costs(commutes)

        assert costs == individual_costs()

    def test_should_compute_costs_from_empty_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory(id=1)
        dede_commute = modal_commute_data
        valoche = geo_employee_factory(id=2)
        valoche_commute = modal_commute_data
        laser = geo_site_factory(address_details=address(citycode="75102"))
        commutes = [
            consolidated_commute_factory(dede, laser, dede_commute),
            consolidated_commute_factory(valoche, laser, valoche_commute),
        ]

        costs = cost_computer.compute_commutes_costs(commutes)

        assert set(costs.cost_by_employee.keys()) == {dede, valoche}
        assert len(costs.cost_by_employee.values()) == 2

    @pytest.mark.parametrize(
        "car_like_mode",
        [
            TransportMode.CAR,
            TransportMode.ELECTRIC_CAR,
            TransportMode.MOTORCYCLE,
            TransportMode.ELECTRIC_MOTORCYCLE,
        ],
    )
    def test_should_compute_all_costs_from_single_car_like_commutes(
        self,
        car_like_mode,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=car_like_mode,
            duration=ImmutableDict({car_like_mode: 3600}),
            distance=ImmutableDict({car_like_mode: 50000}),
            emission=ImmutableDict({car_like_mode: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=470 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3056 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_agnostic_carpooling_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CARPOOLING,
            duration=ImmutableDict({TransportMode.CARPOOLING: 3600}),
            distance=ImmutableDict({TransportMode.CARPOOLING: 50000}),
            emission=ImmutableDict({TransportMode.CARPOOLING: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=47 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=470 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3056 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / 2 / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / 2 / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_car_commutes_with_mobility_plan_benefits(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute, has_mobility_benefits=True)

        expected_costs = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-788 * euros / yearly,
            ),
            cost(
                kind=CostKind.ABSENTEEISM,
                payer=CostPayer.COMPANY,
                amount=-267 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=470 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3056 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_pt_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.PUBLIC_TRANSPORT,
            duration=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 4000}),
            distance=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 60000}),
            emission=ImmutableDict({TransportMode.PUBLIC_TRANSPORT: 2100}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.PT_SUBSCRIPTION,
                payer=CostPayer.COMPANY,
                amount=1270 * euros / yearly,
            ),
            cost(
                kind=CostKind.PT_SUBSCRIPTION,
                payer=CostPayer.EMPLOYEE,
                amount=1270 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=5.09 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=197.165 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=121.20 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=0 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=56 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=3395 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_walk_commutes(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="44102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.WALK,
            duration=ImmutableDict({TransportMode.WALK: 1600}),
            distance=ImmutableDict({TransportMode.WALK: 2000}),
            emission=ImmutableDict({TransportMode.WALK: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commute)

        expected_costs = (
            cost(
                kind=CostKind.WALK_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=69 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=-486 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=20 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=188 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=1358 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=3729 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    @pytest.mark.parametrize(
        "bike_like_mode",
        [
            TransportMode.BICYCLE,
            TransportMode.ELECTRIC_BICYCLE,
            TransportMode.FAST_BICYCLE,
        ],
    )
    def test_should_compute_all_costs_from_single_bike_like_commutes(
        self,
        bike_like_mode,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="01053"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=bike_like_mode,
            duration=ImmutableDict({bike_like_mode: 2400}),
            distance=ImmutableDict({bike_like_mode: 10000}),
            emission=ImmutableDict({bike_like_mode: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commutes)

        expected_costs = (
            cost(
                kind=CostKind.BIKE_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=339 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.COMPANY,
                amount=300 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=-300 * euros / yearly,
            ),
            cost(
                kind=CostKind.HEALTH_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=-2431 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=94 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=1081 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=2037 * euros / yearly,
            ),
            cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=126 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=3452 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_all_costs_from_single_bike_pt_commute(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="01053"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE_PUBLIC_TRANSPORT,
            duration=ImmutableDict({TransportMode.BICYCLE_PUBLIC_TRANSPORT: 2400}),
            distance=ImmutableDict({TransportMode.BICYCLE_PUBLIC_TRANSPORT: 10000}),
            emission=ImmutableDict({TransportMode.BICYCLE_PUBLIC_TRANSPORT: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commutes)

        assert len(costs) > 0

    def test_should_compute_all_costs_from_single_car_pt_commute(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="01053"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR_PUBLIC_TRANSPORT,
            duration=ImmutableDict({TransportMode.CAR_PUBLIC_TRANSPORT: 2400}),
            distance=ImmutableDict({TransportMode.CAR_PUBLIC_TRANSPORT: 10000}),
            emission=ImmutableDict({TransportMode.CAR_PUBLIC_TRANSPORT: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_commute_cost(commutes)

        assert len(costs) > 0

    def test_should_compute_all_costs_from_carpooler_driver_commute(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3000}),
            distance=ImmutableDict({TransportMode.CAR: 48000}),
            emission=ImmutableDict({TransportMode.CAR: 9000}),
            alternative_arrival_time=ImmutableDict(),
        )
        dede_carpools_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commutes = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)
        groupe_commute = consolidated_commute_factory(
            dede, laser, dede_carpools_to_the_laser
        )
        ride_share_factor = 0.6

        costs = cost_computer.compute_cost_for_carpooling_employee(
            groupe_commute, commutes, ride_share_factor
        )

        expected_costs = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-787 * euros / yearly,
            ),
            cost(
                kind=CostKind.ABSENTEEISM,
                payer=CostPayer.COMPANY,
                amount=-267 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=5248 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=20.78 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=734.375 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=2172 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=559.84 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=45 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=451 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=2546 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * 0.6 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=13582 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.COMPANY,
                amount=300 * euros / yearly,
            ),
            cost(
                kind=CostKind.SUSTAINABLE_MOBILITY_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=-300 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)


class TestComputeCostAdjustmentsForRemoteCommutes:
    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_not_adjust_costs_for_non_trip_related_costs(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-120 * euros / yearly,
            ),
        )
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_remote_commutes_costs([commute])

        expected_costs = (
            cost(
                kind=CostKind.COMPANY_ATTRACTIVENESS,
                payer=CostPayer.COMPANY,
                amount=-120 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs == individual_costs({dede: expected_costs})

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_adjust_costs_for_trip_related_costs(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_remote_commutes_costs([commute])

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=4 / 5 * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs == individual_costs({dede: expected_costs})

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_not_adjust_costs_for_parking_spots_if_not_enough_drivers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory()
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)

        costs = cost_computer.compute_remote_commutes_costs([commute])

        expected_costs = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs == individual_costs({dede: expected_costs})

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_adjust_costs_for_parking_spots_if_enough_drivers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 3600}),
            distance=ImmutableDict({TransportMode.CAR: 50000}),
            emission=ImmutableDict({TransportMode.CAR: 9700}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)
        commutes = [commute]
        for employee_index in range(1, 6):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index),
                    laser,
                    dede_goes_to_the_laser,
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        nb_parking_spots_saved = 1 * adimensional
        nb_remote_workers = 6 * adimensional
        cost_factor = 1 * adimensional - (nb_parking_spots_saved / nb_remote_workers)
        expected_costs = (
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=cost_factor * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_adjust_costs_for_bike_parking_spots_if_enough_drivers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(address_details=address(citycode="75102"))
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)
        commutes = [commute]
        for employee_index in range(1, 6):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), laser, commute_data
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        nb_parking_spots_saved = 1 * adimensional
        nb_remote_workers = 6 * adimensional
        cost_factor = 1 * adimensional - (nb_parking_spots_saved / nb_remote_workers)
        expected_costs = (
            cost(
                kind=CostKind.BIKE_PARKING,
                payer=CostPayer.COMPANY,
                amount=cost_factor * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_not_adjust_rent_costs_if_not_enough_workers(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(address_details=address(citycode="75102"))
        dede_goes_to_the_laser = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, dede_goes_to_the_laser)
        commutes = [commute]

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        expected_costs = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_not_adjust_rent_costs_if_workers_are_split_between_sites(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(id=0, address_details=address(citycode="75102"))
        macoumba = geo_site_factory(id=1, address_details=address(citycode="63170"))
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)
        commutes = [commute]
        for employee_index in range(1, 3):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), laser, commute_data
                )
            )
        for employee_index in range(3, 6):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), macoumba, commute_data
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        expected_costs = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs

    @mock.patch("mobility.workers.cost_computer.CostComputer.compute_commute_cost")
    def test_should_adjust_rent_costs_if_enough_workers_are_on_the_same_site(
        self,
        base_commute_cost_computer,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
        address,
    ):
        base_commute_cost_computer.return_value = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=1000 * euros / yearly,
            ),
        )
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(id=0, address_details=address(citycode="75102"))
        macoumba = geo_site_factory(id=1, address_details=address(citycode="63170"))
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.BICYCLE,
            duration=ImmutableDict({TransportMode.BICYCLE: 360}),
            distance=ImmutableDict({TransportMode.BICYCLE: 5000}),
            emission=ImmutableDict({TransportMode.BICYCLE: 0}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)
        commutes = [commute]
        for employee_index in range(1, 32):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), laser, commute_data
                )
            )
        for employee_index in range(30, 32):
            commutes.append(
                consolidated_commute_factory(
                    geo_employee_factory(id=employee_index), macoumba, commute_data
                )
            )

        costs = cost_computer.compute_remote_commutes_costs(commutes)

        nb_office_spots_saved = (32 // 5) * adimensional
        nb_remote_workers = 32 * adimensional
        cost_factor = 1 * adimensional - (nb_office_spots_saved / nb_remote_workers)
        expected_costs = (
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=cost_factor * 1000 * euros / yearly,
            ),
            cost(
                kind=CostKind.HOME_OFFICE,
                payer=CostPayer.COMPANY,
                amount=600 * euros / yearly,
            ),
        )
        assert costs.cost_by_employee[dede] == expected_costs


class TestCarCostsComputation:
    def test_should_compute_car_costs_to_geo_site(
        self,
        cost_computer,
        geo_employee_factory,
        geo_site_factory,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
    ):
        dede = geo_employee_factory(id=0)
        laser = geo_site_factory(id=0)
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 360}),
            distance=ImmutableDict({TransportMode.CAR: 5000}),
            emission=ImmutableDict({TransportMode.CAR: 2000}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, laser, commute_data)

        costs = cost_computer._compute_car_like_costs(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=1920 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=6.91 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=406 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=1003 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=115.43 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=4 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=305 * euros / yearly,
            ),
            cost(
                kind=CostKind.CAR_PARKING,
                payer=CostPayer.COMPANY,
                amount=760 * euros / yearly,
            ),
            cost(
                kind=CostKind.OFFICE_RENT,
                payer=CostPayer.COMPANY,
                amount=2948 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)

    def test_should_compute_car_costs_to_coworking_site(
        self,
        cost_computer,
        geo_employee_factory,
        coworking_site,
        modal_commute_data_factory,
        consolidated_commute_factory,
        individual_costs,
        cost,
    ):
        dede = geo_employee_factory(id=0)
        la_fabrik = coworking_site()
        commute_data = modal_commute_data_factory(
            best_mode=TransportMode.CAR,
            duration=ImmutableDict({TransportMode.CAR: 360}),
            distance=ImmutableDict({TransportMode.CAR: 5000}),
            emission=ImmutableDict({TransportMode.CAR: 2000}),
            alternative_arrival_time=ImmutableDict(),
        )
        commute = consolidated_commute_factory(dede, la_fabrik, commute_data)

        costs = cost_computer._compute_car_like_costs(commute)

        expected_costs = (
            cost(
                kind=CostKind.CAR_KM_FEE,
                payer=CostPayer.EMPLOYEE,
                amount=1920 * euros / yearly,
            ),
            cost(
                kind=CostKind.NOISE_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=6.91 * euros / yearly,
            ),
            cost(
                kind=CostKind.POLLUTION_DAMAGE,
                payer=CostPayer.SOCIETY,
                amount=406 * euros / yearly,
            ),
            cost(
                kind=CostKind.INDUCED_CONGESTION,
                payer=CostPayer.SOCIETY,
                amount=1003 * euros / yearly,
            ),
            cost(
                kind=CostKind.CARBON_IMPACT,
                payer=CostPayer.SOCIETY,
                amount=115.43 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.COMPANY,
                amount=4 * euros / yearly,
            ),
            cost(
                kind=CostKind.WORK_ACCIDENTS,
                payer=CostPayer.SOCIETY,
                amount=47 * euros / yearly,
            ),
            cost(
                kind=CostKind.TRAVEL_TIME,
                payer=CostPayer.COMPANY,
                amount=305 * euros / yearly,
            ),
            cost(
                kind=CostKind.COWORKING_RENT,
                payer=CostPayer.COMPANY,
                amount=6000 * euros / yearly,
            ),
        )
        assert_costs(costs, expected_costs)
