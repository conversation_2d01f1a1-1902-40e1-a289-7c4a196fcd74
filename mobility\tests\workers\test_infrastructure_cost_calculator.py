from typing import Any

import pytest

from mobility.quantity import euros, yearly
from mobility.workers.infrastructure_cost_calculator import InfrastructureCostCalculator


@pytest.fixture
def calculator() -> InfrastructureCostCalculator:
    return InfrastructureCostCalculator()


class TestCarParkingCostComputation:
    def test_compute_car_parking_infrastructure_cost(
        self, calculator: InfrastructureCostCalculator
    ) -> None:
        cost = calculator.compute_car_parking_infrastructure_cost()

        assert 760 * euros / yearly <= cost <= 761 * euros / yearly


class TestRentOfficeCostComputation:
    def test_compute_office_cost_when_office_in_paris(
        self,
        geo_site_factory: Any,
        address: Any,
        calculator: InfrastructureCostCalculator,
    ) -> None:
        paris = geo_site_factory(address_details=address(citycode="75102"))
        cost = calculator.compute_office_rent_cost(paris)

        assert 13582 * euros / yearly < cost < 13583 * euros / yearly

    def test_compute_office_cost_when_office_in_drome(
        self,
        geo_site_factory: Any,
        address: Any,
        calculator: InfrastructureCostCalculator,
    ) -> None:
        drome = geo_site_factory(address_details=address(citycode="26xxx"))
        cost = calculator.compute_office_rent_cost(drome)

        assert 3855 * euros / yearly < cost < 3856 * euros / yearly

    def test_compute_office_cost_when_office_in_somewhere_very_far_away(
        self,
        geo_site_factory: Any,
        address: Any,
        calculator: InfrastructureCostCalculator,
    ) -> None:
        utopia_city = geo_site_factory(address_details=address(citycode="L@vE"))
        cost = calculator.compute_office_rent_cost(utopia_city)

        assert 2948 * euros / yearly < cost < 2949 * euros / yearly


class TestComputeBicycleParkingCost:
    def test_should_compute_parking_infrastructure_cost_for_bikes(
        self, calculator: InfrastructureCostCalculator
    ) -> None:
        cost = calculator.compute_bicycle_parking_infrastructure_cost()

        assert 126 * euros / yearly <= cost <= 127 * euros / yearly
