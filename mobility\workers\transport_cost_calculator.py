from typing import Tuple

from mobility.ir.geo_study import Address
from mobility.quantity import (
    Quantity,
    euros,
    euros_2000,
    euros_2010,
    euros_2014,
    euros_2017,
    euros_2018,
    euros_2019,
    euros_2020,
    euros_2021,
    euros_2022,
    hours,
    kilometers,
    meters,
    minutes,
    tonEC,
    trip,
    weekly,
    yearly,
)
from mobility.workers.pt_subscription_cost_computer import (
    can_use_only_urban_network,
    compute_inter_urban_price,
    compute_urban_network_price,
)

TURNOVER_BEFORE_MOBILITY_PLAN = 15 / yearly / 100
TURNOVER_AFTER_MOBILITY_PLAN = 13 / yearly / 100
MONTHLY_MEDIAN_SALARY = 3_180 * euros_2019
YEARLY_MEDIAN_SALARY = MONTHLY_MEDIAN_SALARY * 12
YEARLY_MEAN_SUPER_GROSS_SALARY = 51808 * euros_2019
EMPLOYEE_TURNOVER_COST = 6 * MONTHLY_MEDIAN_SALARY
ABSENTEEISM_RATE_BEFORE_MOBILITY_PLAN = 5 / yearly / 100
ABSENTEEISM_RATE_AFTER_MOBILITY_PLAN = 475 / yearly / 10000
ABSENTEEISM_COST = YEARLY_MEAN_SUPER_GROSS_SALARY * 2

liters = meters * meters * meters / 1000
CAR_KM_FIXED_FEE = 1_372 * euros_2017 / yearly
DIESEL_PRICE = 1.8831 * euros / liters
PETROL_PRICE = 1.8713 * euros / liters
LONG_RANGE_DIESEL_CONSUMPTION = 4.7 * liters / (100 * kilometers)
LONG_RANGE_PETROL_CONSUMPTION = 6.3 * liters / (100 * kilometers)
MEDIUM_RANGE_DIESEL_CONSUMPTION = 5.6 * liters / (100 * kilometers)
MEDIUM_RANGE_PETROL_CONSUMPTION = 7.4 * liters / (100 * kilometers)
SHORT_RANGE_DIESEL_CONSUMPTION = 6.1 * liters / (100 * kilometers)
SHORT_RANGE_PETROL_CONSUMPTION = 8.3 * liters / (100 * kilometers)
DIESEL_CAR_PART = 0.6
PETROL_CAR_PART = 0.4
LONG_RANGE_CAR_KM_FEE = (
    PETROL_CAR_PART * LONG_RANGE_PETROL_CONSUMPTION * PETROL_PRICE
    + DIESEL_CAR_PART * LONG_RANGE_DIESEL_CONSUMPTION * DIESEL_PRICE
)
MEDIUM_RANGE_CAR_KM_FEE = (
    PETROL_CAR_PART * MEDIUM_RANGE_PETROL_CONSUMPTION * PETROL_PRICE
    + DIESEL_CAR_PART * MEDIUM_RANGE_DIESEL_CONSUMPTION * DIESEL_PRICE
)
SHORT_RANGE_CAR_KM_FEE = (
    PETROL_CAR_PART * SHORT_RANGE_PETROL_CONSUMPTION * PETROL_PRICE
    + DIESEL_CAR_PART * SHORT_RANGE_DIESEL_CONSUMPTION * DIESEL_PRICE
)
CAR_MAINTENANCE_PART = 0.508
SHORT_RANGE_CAR_NOISE_COST = 2.58 * euros_2010 / kilometers / 1000
MEDIUM_RANGE_CAR_NOISE_COST = 1.89 * euros_2010 / kilometers / 1000
LONG_RANGE_CAR_NOISE_COST = 0.2 * euros_2010 / kilometers / 1000
SHORT_RANGE_PT_NOISE_COST = 0.99 * euros_2010 / kilometers / 1000
MEDIUM_RANGE_PT_NOISE_COST = 0.23 * euros_2010 / kilometers / 1000
LONG_RANGE_PT_NOISE_COST = 0.05 * euros_2010 / kilometers / 1000
SHORT_RANGE_CAR_POLLUTION_COST = 17.30 * euros / kilometers / 100
MEDIUM_RANGE_CAR_POLLUTION_COST = 1.90 * euros / kilometers / 100
LONG_RANGE_CAR_POLLUTION_COST = 1.45 * euros / kilometers / 100
SHORT_RANGE_PT_POLLUTION_COST = 5.14 * euros / kilometers / 100
MEDIUM_RANGE_PT_POLLUTION_COST = 0.50 * euros / kilometers / 100
LONG_RANGE_PT_POLLUTION_COST = 0.25 * euros / kilometers / 100
RATE_OF_TRANSPORT_TIME_THAT_WOULD_BE_SPENT_WORKING = 0.2
YEARLY_TIME_SPENT_AT_WORK = 35 * hours * yearly / weekly
SUPER_GROSS_SALARY_PER_TIME = YEARLY_MEAN_SUPER_GROSS_SALARY / YEARLY_TIME_SPENT_AT_WORK
TIME_COST = (
    SUPER_GROSS_SALARY_PER_TIME * RATE_OF_TRANSPORT_TIME_THAT_WOULD_BE_SPENT_WORKING
)
SHORT_RANGE_CAR_CONGESTION_TIME_LOST = 3.94 * minutes / kilometers
MEDIUM_RANGE_CAR_CONGESTION_TIME_LOST = 1.35 * minutes / kilometers
LONG_RANGE_CAR_CONGESTION_TIME_LOST = 0.27 * minutes / kilometers
CARBON_COST = 122.8 * euros / tonEC
HEALTH_COST_FOR_SOCIETY = 2980 * euros_2021 / yearly
HEALTH_IMPACT_COST_OF_ACTIVE_MODE = 0.484 * euros_2014 / kilometers
WALK_SOCIETY_WORK_ACCIDENT_COST = 0.2 * euros_2022 / kilometers
BICYCLE_SOCIETY_WORK_ACCIDENT_COST = 0.23 * euros_2022 / kilometers
PT_SOCIETY_WORK_ACCIDENT_COST = 0.002 * euros_2022 / kilometers
CAR_SOCIETY_WORK_ACCIDENT_COST = 0.02 * euros_2022 / kilometers
WALK_COMPANY_WORK_ACCIDENT_COST = 0.022 * euros_2022 / kilometers
BICYCLE_COMPANY_WORK_ACCIDENT_COST = 0.02 * euros_2022 / kilometers
PT_COMPANY_WORK_ACCIDENT_COST = 0.0 * euros_2022 / kilometers
CAR_COMPANY_WORK_ACCIDENT_COST = 0.002 * euros_2022 / kilometers
CAR_PARKING_COST = 745 * euros_2020 / yearly
PT_SUBSCRIPTION_COST_EMPLOYEE_PART = 0.5
PT_SUBSCRIPTION_COST_COMPANY_PART = 0.5
WALK_KM_FEE = 0.055 * euros_2000 / kilometers
BICYCLE_KM_FEE = 0.0535 * euros_2000 / kilometers
SUSTAINABLE_MOBILITY_FEE = 300 * euros / yearly
BICYCLE_PARKING_YEARLY_COST = 124 * euros_2020 / yearly
COWORKING_YEARLY_RENT_COST = 500 * euros * 12 / yearly
HEALTH_INSURANCE_COST_FOR_EMPLOYEE = 455 * euros_2018 / yearly
HEALTH_INSURANCE_COST_FOR_COMPANY = 774 * euros_2018 / yearly
HEALTH_INSURANCE_COST_FOR_EMPLOYEE_WITH_BENEFITS = 410 * euros_2018 / yearly
HEALTH_INSURANCE_COST_FOR_COMPANY_WITH_BENEFITS = 697 * euros_2018 / yearly
SURFACE_REQUIRED_BY_EMPLOYEE = 18 * meters * meters


class TransportCostCalculator:
    def split_trip_by_distances(
        self, distance: Quantity
    ) -> Tuple[Quantity, Quantity, Quantity]:
        short_range_distance = min(5 * kilometers, distance)
        medium_range_distance = max(
            0 * kilometers, min(10 * kilometers, distance - 5 * kilometers)
        )
        long_range_distance = max(0 * kilometers, distance - 15 * kilometers)
        return (
            short_range_distance,
            abs(medium_range_distance),
            abs(long_range_distance),
        )

    def compute_car_km_fee(self, distance: Quantity) -> Quantity:
        short_distance, medium_distance, long_distance = self.split_trip_by_distances(
            distance
        )
        gas_consumption_cost = (
            short_distance * SHORT_RANGE_CAR_KM_FEE
            + medium_distance * MEDIUM_RANGE_CAR_KM_FEE
            + long_distance * LONG_RANGE_CAR_KM_FEE
        )
        return (
            CAR_KM_FIXED_FEE + (1 + CAR_MAINTENANCE_PART) * gas_consumption_cost / trip
        )

    def compute_car_noise_damage_cost(self, distance: Quantity) -> Quantity:
        return self.compute_noise_damage_cost(
            distance,
            SHORT_RANGE_CAR_NOISE_COST,
            MEDIUM_RANGE_CAR_NOISE_COST,
            LONG_RANGE_CAR_NOISE_COST,
        )

    def compute_pt_noise_damage_cost(self, distance: Quantity) -> Quantity:
        return self.compute_noise_damage_cost(
            distance,
            SHORT_RANGE_PT_NOISE_COST,
            MEDIUM_RANGE_PT_NOISE_COST,
            LONG_RANGE_PT_NOISE_COST,
        )

    def compute_noise_damage_cost(
        self,
        distance: Quantity,
        short_range_fee: Quantity,
        medium_range_fee: Quantity,
        long_range_fee: Quantity,
    ) -> Quantity:
        short_distance, medium_distance, long_distance = self.split_trip_by_distances(
            distance
        )
        return (
            short_distance * short_range_fee
            + medium_distance * medium_range_fee
            + long_distance * long_range_fee
        ) / trip

    def compute_car_pollution_damage_cost(self, distance: Quantity) -> Quantity:
        return self.compute_pollution_damage_cost(
            distance,
            SHORT_RANGE_CAR_POLLUTION_COST,
            MEDIUM_RANGE_CAR_POLLUTION_COST,
            LONG_RANGE_CAR_POLLUTION_COST,
        )

    def compute_pt_pollution_damage_cost(self, distance: Quantity) -> Quantity:
        return self.compute_pollution_damage_cost(
            distance,
            SHORT_RANGE_PT_POLLUTION_COST,
            MEDIUM_RANGE_PT_POLLUTION_COST,
            LONG_RANGE_PT_POLLUTION_COST,
        )

    def compute_pollution_damage_cost(
        self,
        distance: Quantity,
        short_range_fee: Quantity,
        medium_range_fee: Quantity,
        long_range_fee: Quantity,
    ) -> Quantity:
        short_distance, medium_distance, long_distance = self.split_trip_by_distances(
            distance
        )
        return (
            short_distance * short_range_fee
            + medium_distance * medium_range_fee
            + long_distance * long_range_fee
        ) / trip

    def compute_car_congestion_damage_cost(self, distance: Quantity) -> Quantity:
        short_distance, medium_distance, long_distance = self.split_trip_by_distances(
            distance
        )
        short_range_time_lost = SHORT_RANGE_CAR_CONGESTION_TIME_LOST
        medium_range_time_lost = MEDIUM_RANGE_CAR_CONGESTION_TIME_LOST
        long_range_time_lost = LONG_RANGE_CAR_CONGESTION_TIME_LOST
        return (
            (
                short_distance * short_range_time_lost
                + medium_distance * medium_range_time_lost
                + long_distance * long_range_time_lost
            )
            * TIME_COST
            / trip
        )

    def compute_carbone_cost(self, emission: Quantity) -> Quantity:
        return emission * CARBON_COST / trip

    def compute_travel_time_cost(self, duration: Quantity) -> Quantity:
        return duration * TIME_COST / trip

    def compute_walk_km_fee(self, distance: Quantity) -> Quantity:
        return WALK_KM_FEE * distance / trip

    def compute_bicycle_km_fee(self, distance: Quantity) -> Quantity:
        return BICYCLE_KM_FEE * distance / trip

    def compute_pt_subscription_cost(
        self, origin: Address, destination: Address, distance: Quantity
    ) -> Quantity:
        return self.compute_pt_subscription_cost_from_citycodes(
            origin.citycode, destination.citycode, distance
        )

    def compute_pt_subscription_cost_from_citycodes(
        self, origin_citycode: str, destination_citycode: str, distance: Quantity
    ) -> Quantity:
        urban_network_price = compute_urban_network_price(destination_citycode)
        if can_use_only_urban_network(origin_citycode, destination_citycode):
            return urban_network_price
        inter_urban_price = compute_inter_urban_price(distance)
        return urban_network_price + inter_urban_price

    def compute_pt_subscription_cost_for_employee(
        self, origin: Address, destination: Address, distance: Quantity
    ) -> Quantity:
        return (
            self.compute_pt_subscription_cost(origin, destination, distance)
            * PT_SUBSCRIPTION_COST_EMPLOYEE_PART
        )

    def compute_pt_subscription_cost_for_company(
        self, origin: Address, destination: Address, distance: Quantity
    ) -> Quantity:
        return (
            self.compute_pt_subscription_cost(origin, destination, distance)
            * PT_SUBSCRIPTION_COST_COMPANY_PART
        )
