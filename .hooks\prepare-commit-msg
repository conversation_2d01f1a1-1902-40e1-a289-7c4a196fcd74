#!/bin/bash

COMMIT_MSG_FILE=$1
COMMIT_SOURCE=$2
SHA1=$3

declare -A name_emoji
name_emoji=(['.']='✒️ linting 🏭 refactoring 🔧 tweak 🪲 bug fix 🎨 beautify'
	    ['serializer']='🗺️ mapping'
            ['api_abstraction/']='🔮 :crystal_ball:'
            ['audit/']='🎯 :dart:'
            ['internal_api/']='⏰ :clock1:'
            ['mobility/']='🚚 :truck:'
            ['webservice/']='🌋 :volcano:'
	    ['test_']='⚗️ :alembic:'
	    ['vendors']='🙈 :see_no_evil:'
            ['doc']='📚 :books:'
            ['.rst']='📚 :books:'
	    ['scripts/']='⚙️'
           )

touched_files=$(git diff --name-only --staged)

if [[ -z $COMMIT_SOURCE ]] || [[ $COMMIT_SOURCE == "commit"* ]]
then
        for pattern in ${!name_emoji[@]}
        do
            if [ $(echo "$touched_files" | grep -c -e "$pattern") -ne 0 ]
            then
                sed -i "1s;^;# ${name_emoji[$pattern]} (touched $pattern)\n;" \
                    "$COMMIT_MSG_FILE"
            fi
        done

        sed -i "1s;^;# Emoji Suggestions:\n;" "$COMMIT_MSG_FILE"
fi
