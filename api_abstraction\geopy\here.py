import logging
from typing import Optional

import geopy
from geopy.geocoders import HereV7 as Here

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import ApiFail, ApiTimeout, GeocodeApi
from mobility.ir.geo_study import Address, GeoCoordinates


class HereGeocoder(GeocodeApi):
    max_call_per_period = 10

    def __init__(self, token: str, reporter: EventReporter):
        super().__init__(token, reporter)
        self._geocoder = Here(apikey=token)

    def _geocode(self, address: str) -> GeoCoordinates:
        try:
            geoloc = self._geocoder.geocode(address)
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot find geolocalization of {address}")
        return GeoCoordinates(latitude=geoloc.latitude, longitude=geoloc.longitude)

    def _reverse(self, coords: GeoCoordinates) -> Address:
        try:
            geoloc = self._geocoder.reverse((coords.latitude, coords.longitude))
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot reverse geolocalization {coords}")
        return self._extract_address_from_geoloc(geoloc)

    def _geocode_details(self, address: str) -> Address:
        try:
            geoloc = self._geocoder.geocode(address)
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot find geolocalization of {address}")
        return self._extract_address_from_geoloc(geoloc, address)

    def _extract_address_from_geoloc(
        self, geoloc: geopy.location, origin_address: Optional[str] = None
    ) -> Address:
        if "address" not in geoloc.raw:
            raise ApiFail(f"No address field in raw geoloc {geoloc.raw}")
        if "postalCode" not in geoloc.raw["address"]:
            postalcode = "00000"
        else:
            postalcode = geoloc.raw["address"]["postalCode"]
        if "city" not in geoloc.raw["address"]:
            raise ApiFail(f"No city name in raw geoloc {geoloc.raw}")
        if (
            "scoring" not in geoloc.raw
            or "queryScore" not in geoloc.raw["scoring"]
            or float(geoloc.raw["scoring"]["queryScore"]) < 0.45
        ):
            logging.warning(
                f"Geolocation might be imprecise: {origin_address} => {geoloc.address} ({geoloc.latitude}, {geoloc.longitude})"
            )
        return Address(
            full=geoloc.address if origin_address is None else origin_address,
            normalized=geoloc.address,
            city=geoloc.raw["address"]["city"],
            postcode=postalcode,
            citycode=postalcode,
            coordinates=GeoCoordinates(
                latitude=geoloc.latitude, longitude=geoloc.longitude
            ),
        )
