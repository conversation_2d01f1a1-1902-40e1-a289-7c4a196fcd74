#!/usr/bin/env python3
"""
4-Level Aggregation/Disaggregation Sankey Diagram for Strasbourg Mobility Flows
Demonstrates hierarchical flow visualization with aggregation→disaggregation pattern.
"""

import os
import pandas as pd
from mobility.serializers.charters.flow_chart_grapher import FlowChartGrapher
from mobility.serializers.charters.svg_interface import SVGFigure, SVGNodes, SVGLabel, SVGFont


def create_strasbourg_aggregation_flow():
    """Create a combined figure showing the complete aggregation/disaggregation story."""

    # CSV file path - use the file in the workspace
    csv_path = "hv_aggregated_od_matrix_total_RN4_Est_Ouest.csv"

    # Output directory
    output_dir = "output_flow_charts"
    os.makedirs(output_dir, exist_ok=True)

    # Read CSV data
    print(f"Reading CSV data from: {csv_path}")
    df = pd.read_csv(csv_path)
    print(f"Loaded {len(df)} rows of flow data")

    # Helper function for department mapping
    def get_department(zone_name):
        if zone_name.startswith("Strasbourg"):
            return "Strasbourg"
        else:
            return zone_name

    # Create department-level aggregated flows
    df_dept = df.copy()
    df_dept['origin_dept'] = df_dept['origin_zone'].apply(get_department)
    df_dept['destination_dept'] = df_dept['destination_zone'].apply(get_department)
    dept_flows = df_dept.groupby(['origin_dept', 'destination_dept'])['count'].sum().reset_index()

    # Prepare data for each chart

    # Chart 1: Strasbourg Communes → Strasbourg Department (Level 1→2) - REVERSED DIRECTION
    strasbourg_origins = df[df['origin_zone'].str.startswith('Strasbourg')].copy()
    level1_2_data = strasbourg_origins.groupby('origin_zone')['count'].sum().reset_index()

    level1_2_flows = []
    for _, row in level1_2_data.iterrows():
        # Reverse direction: commune → "Strasbourg"
        level1_2_flows.extend([(row['origin_zone'], "Strasbourg")] * row['count'])

    # Chart 2: Department Level Flows (Level 2→3)
    level2_3_flows = []
    for _, row in dept_flows.iterrows():
        level2_3_flows.extend([(row['origin_dept'], row['destination_dept'])] * row['count'])

    # Chart 3: Strasbourg Department → Strasbourg Communes (Level 3→4)
    strasbourg_destinations = df[df['destination_zone'].str.startswith('Strasbourg')].copy()
    level3_4_data = strasbourg_destinations.groupby('destination_zone')['count'].sum().reset_index()

    level3_4_flows = []
    for _, row in level3_4_data.iterrows():
        level3_4_flows.extend([("Strasbourg", row['destination_zone'])] * row['count'])

    # Create grapher instance
    grapher = FlowChartGrapher[str](output_dir)

    # Generate colors and categories for each chart
    all_strasbourg = sorted([z for z in df['origin_zone'].unique() if z.startswith('Strasbourg')])
    all_departments = sorted(dept_flows['origin_dept'].unique())

    strasbourg_colors = {"Strasbourg": "#FF9800"}
    color_palette = ["#E53935", "#1E88E5", "#43A047", "#9C27B0", "#FF5722", "#795548", "#607D8B"]
    for i, commune in enumerate(all_strasbourg):
        strasbourg_colors[commune] = color_palette[i % len(color_palette)]

    dept_colors = {}
    dept_palette = ["#4CAF50", "#2196F3", "#FF9800", "#9C27B0", "#00BCD4", "#8BC34A", "#FFC107"]
    for i, dept in enumerate(all_departments):
        if dept == "Strasbourg":
            dept_colors[dept] = "#FF9800"
        else:
            dept_colors[dept] = dept_palette[i % len(dept_palette)]

    # Create label mappings
    strasbourg_labels = {k: k.replace("Strasbourg ", "").replace("Strasbourg", "Strasbourg") for k in all_strasbourg + ["Strasbourg"]}
    dept_labels = {k: k for k in all_departments}

    # Create individual chart components (but don't write them to files yet)
    print("Creating chart components...")

    # Chart 1 components
    flow1 = grapher.compute_flows(level1_2_flows, all_strasbourg + ["Strasbourg"])
    fig1 = grapher.make_svg_figure(
        "Niveau 1→2: Communes → Département",
        flow1,
        ("Communes Strasbourg", "Département Strasbourg"),
        strasbourg_colors,
        all_strasbourg + ["Strasbourg"],
        strasbourg_labels,
        {}
    )

    # Chart 2 components
    flow2 = grapher.compute_flows(level2_3_flows, all_departments)
    fig2 = grapher.make_svg_figure(
        "Niveau 2→3: Flux Inter-Départements",
        flow2,
        ("Départements Origine", "Départements Destination"),
        dept_colors,
        all_departments,
        dept_labels,
        {}
    )

    # Chart 3 components
    flow3 = grapher.compute_flows(level3_4_flows, ["Strasbourg"] + all_strasbourg)
    fig3 = grapher.make_svg_figure(
        "Niveau 3→4: Département → Communes",
        flow3,
        ("Département Strasbourg", "Communes Strasbourg"),
        strasbourg_colors,
        ["Strasbourg"] + all_strasbourg,
        strasbourg_labels,
        {}
    )

    # Now combine the three figures into one comprehensive figure
    print("Combining charts into single figure...")

    # Calculate spacing between charts
    chart_spacing = 100  # Space between charts

    # Position charts horizontally
    # Chart 1 at x=0
    chart1_nodes = fig1.figure

    # Chart 2 at x = chart1_width + spacing
    chart1_width = fig1.width
    chart2_nodes = fig2.figure.translate(chart1_width + chart_spacing, 0)

    # Chart 3 at x = chart1_width + chart2_width + 2*spacing
    chart2_width = fig2.width
    chart3_nodes = fig3.figure.translate(chart1_width + chart2_width + 2*chart_spacing, 0)

    # Combine all components
    combined_body = chart1_nodes + chart2_nodes + chart3_nodes

    # Calculate total figure dimensions
    total_width = chart1_width + chart2_width + fig3.width + 2*chart_spacing
    total_height = max(fig1.height, fig2.height, fig3.height)

    # Create overall title
    title_font = grapher.main_title_font
    main_title = SVGLabel(
        "Flux OD PL Strasbourg RN4 Est-Ouest: Agrégation/Désagrégation Complète",
        title_font,
        anchor_x=total_width/2,
        anchor_y=30,
        color="#000000"
    )

    # Create the combined figure
    combined_figure = SVGFigure(
        figure=SVGNodes((main_title,)) + combined_body,
        width=total_width,
        height=total_height + 60  # Extra space for main title
    )

    # Write the combined figure
    output_file = grapher.writer.write_svg(combined_figure, "strasbourg_complete_aggregation_flow.svg")

    print(f"Combined aggregation/disaggregation flow chart generated: {output_file}")
    print("Chart shows complete flow pattern:")
    print("  Left: Strasbourg Communes → Strasbourg Department")
    print("  Center: Department ↔ Department flows")
    print("  Right: Strasbourg Department → Strasbourg Communes")

    return output_file


if __name__ == "__main__":
    create_strasbourg_aggregation_flow()