from datetime import date

from limiter import Limiter

from api_abstraction.api.event_reporter import Burst, EventReporter


class ApiFail(Exception):
    """Umbrella API failure exception

    All other API related exceptions should inherit from it to
    be able to catch them all.
    `message` can describe the context of the issue resulting in an exception
    """

    def __init__(self, message: str):
        self.message = message

    def __str__(self) -> str:
        return f"API Failure {': ' + self.message if self.message else ''}"


class ApiTimeout(ApiFail):
    pass


class ApiInapt(ApiFail):
    pass


class AbstractAPI:
    max_call_per_period = 100
    period_in_seconds = 1.0
    retries = 10
    retry_delay_in_seconds = 0.1
    token = ""
    is_trivial = False

    def __init__(self, token: str, reporter: EventReporter) -> None:
        self.rate_limiter = Limiter(
            rate=self.max_call_per_period // self.period_in_seconds,
            consume=1,
            capacity=self.max_call_per_period,
        )
        self.token = token
        self.event_report = reporter
        self._get_today = lambda: date.today()

    def new_burst(self) -> Burst:
        return self.event_report.new_burst(self.__class__.__name__)
