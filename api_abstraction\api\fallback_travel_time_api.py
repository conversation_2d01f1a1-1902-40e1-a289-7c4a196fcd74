from functools import cache
from typing import List, Optional, Tuple

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


class FallbackTravelTimeApi(TravelTimeApi):
    def __init__(self, fallback_api_chain: List[TravelTimeApi]):
        if len(fallback_api_chain) < 2:
            raise ValueError("Fallback should get at least two APIs")
        self.fallback_chain = fallback_api_chain

    @cache
    def time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        for api in self.fallback_chain:
            try:
                return api.time(origin, destination, mode, arrival_time, max_transfers)
            except ApiInapt:
                continue
        raise ApiFail("No recorded API was apt")
