from dataclasses import dataclass
from datetime import datetime
from typing import Any, Optional

from api_abstraction.api.travel_time_api import JourneyAttribute
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


@dataclass(frozen=True, eq=True)
class GoogleRequestParameters:
    origin: GeoCoordinates
    destination: GeoCoordinates
    mode: TransportMode
    departure_time: Optional[datetime] = None
    arrival_time: Optional[datetime] = None
    traffic_model: str = "pessimistic"
    routing_preference: str = "TRAFFIC_AWARE_OPTIMAL"


class GoogleClientAPI:
    def __init__(self) -> None:
        self.car_like_modes = [
            TransportMode.CAR,
            TransportMode.ELECTRIC_CAR,
            TransportMode.CARPOOLING,
            TransportMode.MOTORCYCLE,
        ]

    def compute_route(self, parameters: GoogleRequestParameters) -> JourneyAttribute:
        raise NotImplementedError()

    def _format_result(
        self, result: Any, mode: Optional[TransportMode]
    ) -> JourneyAttribute:
        raise NotImplementedError()
