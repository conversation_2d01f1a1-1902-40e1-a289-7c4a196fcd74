from typing import Any

import pytest

from mobility.quantity import euros, gramEC, kilometers, minutes, seconds, yearly
from mobility.workers.transport_cost_calculator import TransportCostCalculator


@pytest.fixture
def calculator() -> TransportCostCalculator:
    return TransportCostCalculator()


class TestNoiseCostComputation:
    def test_should_compute_car_noise_km_cost_for_no_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_noise_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_pt_noise_km_cost_for_no_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_noise_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_car_noise_cost_for_short_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_noise_damage_cost(3 * kilometers)

        assert 4 * euros / yearly <= cost <= 5 * euros / yearly

    def test_should_compute_pt_noise_cost_for_short_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_noise_damage_cost(3 * kilometers)

        assert 1 * euros / yearly <= cost <= 2 * euros / yearly

    def test_should_compute_car_noise_cost_for_medium_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_noise_damage_cost(10 * kilometers)

        assert 11 * euros / yearly <= cost <= 12 * euros / yearly

    def test_should_compute_pt_noise_cost_for_medium_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_noise_damage_cost(10 * kilometers)

        assert 3 * euros / yearly <= cost <= 4 * euros / yearly

    def test_should_compute_car_noise_cost_for_long_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_noise_damage_cost(40 * kilometers)

        assert 19 * euros / yearly <= cost <= 20 * euros / yearly

    def test_should_compute_pt_noise_cost_for_long_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_noise_damage_cost(40 * kilometers)

        assert 4 * euros / yearly <= cost <= 5 * euros / yearly


class TestPollutionCostComputation:
    def test_should_compute_car_pollution_km_cost_for_no_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_pollution_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_pt_pollution_km_cost_for_no_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_pollution_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_car_pollution_cost_for_short_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_pollution_damage_cost(3 * kilometers)

        assert 243 * euros / yearly <= cost <= 244 * euros / yearly

    def test_should_compute_pt_pollution_cost_for_short_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_pollution_damage_cost(3 * kilometers)

        assert 72 * euros / yearly <= cost <= 73 * euros / yearly

    def test_should_compute_car_pollution_cost_for_medium_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_pollution_damage_cost(10 * kilometers)

        assert 451 * euros / yearly <= cost <= 452 * euros / yearly

    def test_should_compute_pt_pollution_cost_for_medium_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_pollution_damage_cost(10 * kilometers)

        assert 132 * euros / yearly <= cost <= 133 * euros / yearly

    def test_should_compute_car_pollution_cost_for_long_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_pollution_damage_cost(40 * kilometers)

        assert 666 * euros / yearly <= cost <= 667 * euros / yearly

    def test_should_compute_pt_pollution_cost_for_long_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_pt_pollution_damage_cost(40 * kilometers)

        assert 173 * euros / yearly <= cost <= 174 * euros / yearly


class TestCongestionCostComputation:
    def test_should_compute_congestion_km_cost_for_no_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_congestion_damage_cost(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_should_compute_car_congestion_cost_for_short_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_congestion_damage_cost(3 * kilometers)

        assert 602 * euros / yearly <= cost <= 603 * euros / yearly

    def test_should_compute_car_congestion_cost_for_medium_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_congestion_damage_cost(10 * kilometers)

        assert 1347 * euros / yearly <= cost <= 1348 * euros / yearly

    def test_should_compute_car_congestion_cost_for_long_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_congestion_damage_cost(40 * kilometers)

        assert 2034 * euros / yearly <= cost <= 2035 * euros / yearly


class TestCarbonCostComputation:
    def test_should_compute_carbon_cost_when_no_emission(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_carbone_cost(0 * gramEC)

        assert cost == 0 * euros / yearly

    def test_should_compute_carbon_cost(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_carbone_cost(1900 * gramEC)

        assert 109 * euros / yearly <= cost <= 110 * euros / yearly


class TestTravelTimeCostComputation:
    def test_should_compute_travel_time_cost_when_no_travel(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_travel_time_cost(0 * minutes)

        assert cost == 0 * euros / yearly

    def test_should_compute_travel_time_cost(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_travel_time_cost(3600 * seconds)

        assert 3056 * euros / yearly <= cost <= 3057 * euros / yearly


class TestComputeActiveModesKmFee:
    def test_compute_null_walk_km_fee_for_null_distance(
        self, calculator: TransportCostCalculator
    ):
        cost = calculator.compute_walk_km_fee(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_compute_null_bicycle_km_fee_for_null_distance(
        self, calculator: TransportCostCalculator
    ):
        cost = calculator.compute_bicycle_km_fee(0 * kilometers)

        assert cost == 0 * euros / yearly

    def test_compute_some_walk_km_fee_for_non_null_distance(
        self, calculator: TransportCostCalculator
    ):
        cost = calculator.compute_walk_km_fee(2 * kilometers)

        assert 69 * euros / yearly <= cost <= 70 * euros / yearly

    def test_compute_some_bicycle_km_fee_for_non_null_distance(
        self, calculator: TransportCostCalculator
    ):
        cost = calculator.compute_bicycle_km_fee(10 * kilometers)

        assert 339 * euros / yearly <= cost <= 340 * euros / yearly


class TestPTSubscriptionComputation:
    def test_compute_urban_pt_subscription_when_short_distance_traveled_inside_paris(
        self, address: Any, calculator: TransportCostCalculator
    ):
        dede = address(citycode="75101")
        laser = address(citycode="75102")

        cost = calculator.compute_pt_subscription_cost(dede, laser, 3 * kilometers)

        assert 902 * euros / yearly <= cost <= 903 * euros / yearly

    def test_compute_urban_pt_subscription_when_short_distance_traveled_inside_marseille(
        self, address: Any, calculator: TransportCostCalculator
    ):
        dede = address(citycode="13214")
        laser = address(citycode="13202")

        cost = calculator.compute_pt_subscription_cost(dede, laser, 3 * kilometers)

        assert 876 * euros / yearly <= cost <= 877 * euros / yearly

    def test_compute_inter_urban_pt_subscription_when_medium_distance_traveled(
        self, address: Any, calculator: TransportCostCalculator
    ):
        dede = address(citycode="xxxxx")
        laser = address(citycode="75102")

        cost = calculator.compute_pt_subscription_cost(dede, laser, 20 * kilometers)

        assert 1489 * euros / yearly <= cost <= 1490 * euros / yearly

    def test_compute_inter_urban_pt_subscription_when_medium_distance_traveled_with_no_urban_network(
        self, address: Any, calculator: TransportCostCalculator
    ):
        dede = address(citycode="Lolol")
        laser = address(citycode="wtf")

        cost = calculator.compute_pt_subscription_cost(dede, laser, 20 * kilometers)

        assert 587 * euros / yearly <= cost <= 588 * euros / yearly

    def test_compute_inter_urban_pt_subscription_when_medium_distance_traveled_with_urban_network_at_destination(
        self, address: Any, calculator: TransportCostCalculator
    ):
        dede = address(citycode="26333")
        laser = address(citycode="69386")

        cost = calculator.compute_pt_subscription_cost(dede, laser, 72 * kilometers)

        assert 2749 * euros / yearly <= cost <= 2750 * euros / yearly


class TestComputeCarKmFee:
    def test_should_compute_car_km_cost_for_no_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_km_fee(0 * kilometers)

        assert 1_456 * euros / yearly <= cost <= 1_457 * euros / yearly

    def test_should_compute_car_km_cost_for_short_range(
        self, calculator: TransportCostCalculator
    ) -> None:

        cost = calculator.compute_car_km_fee(3 * kilometers)

        assert 1_734 * euros / yearly <= cost <= 1_735 * euros / yearly

    def test_should_compute_car_km_cost_for_medium_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_km_fee(10 * kilometers)

        assert 2341 * euros / yearly <= cost <= 2342 * euros / yearly

    def test_should_compute_car_km_cost_for_long_range(
        self, calculator: TransportCostCalculator
    ) -> None:
        cost = calculator.compute_car_km_fee(40 * kilometers)

        assert 4538 * euros / yearly <= cost <= 4539 * euros / yearly
