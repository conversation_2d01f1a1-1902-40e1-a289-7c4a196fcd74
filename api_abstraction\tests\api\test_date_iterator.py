from datetime import date, datetime

import pytest

from api_abstraction.api.date_iterators import (
    DateTimeInterval,
    DateTimeIntervals,
    DayInInterval,
    DayOfWeek,
)


class TestDayOfWeek:
    def test_day_of_week_should_iterate_on_mondays(self) -> None:
        dow = DayOfWeek(date(2020, 1, 7), 0, 10, 22, 13)

        assert next(dow) == datetime(2020, 1, 13, 10, 22)
        assert next(dow) == datetime(2020, 1, 6, 10, 22)
        assert len(list(dow)) == 11


class TestDateTimeInterval:
    def test_interval_contains_obvious_day(self) -> None:
        dti = DateTimeInterval(datetime(2020, 1, 1), datetime(2020, 1, 5))

        assert datetime(2020, 1, 3) in dti

    def test_interval_does_not_contain_top_edge_day(self) -> None:
        dti = DateTimeInterval(datetime(2020, 1, 1), datetime(2020, 1, 5))

        assert datetime(2020, 1, 5) not in dti

    def test_interval_does_not_contain_low_edge_day(self) -> None:
        dti = DateTimeInterval(datetime(2020, 1, 1), datetime(2020, 1, 5))

        assert datetime(2020, 1, 1) not in dti

    def test_interval_contains_just_above_low_edge_day(self) -> None:
        dti = DateTimeInterval(datetime(2020, 1, 1), datetime(2020, 1, 5))

        assert datetime(2020, 1, 1, 0, 0, 0, 1) in dti

    def test_interval_contains_just_below_top_edge_day(self) -> None:
        dti = DateTimeInterval(datetime(2020, 1, 1), datetime(2020, 1, 5))

        assert datetime(2020, 1, 4, 23, 59, 59, 10**6 - 1) in dti

    def test_remove_day_can_remove_everything(self) -> None:
        dti = DateTimeInterval(
            datetime(2020, 2, 2, 8, 30), datetime(2020, 2, 2, 16, 22)
        )

        hollow = dti.remove_day(datetime(2020, 2, 2))

        assert hollow == []


class TestDateTimeIntervals:
    def tests_intervals_contain_obvious_days(self) -> None:
        dtis = DateTimeIntervals(
            [
                DateTimeInterval(datetime(2020, 2, 2), datetime(2020, 2, 15)),
                DateTimeInterval(datetime(2020, 2, 23), datetime(2020, 3, 5)),
            ]
        )

        assert datetime(2020, 2, 7) in dtis
        assert datetime(2020, 3, 2) in dtis

    def test_hollow_intervals_do_not_contain_day_in_hole(self) -> None:
        dtis = DateTimeIntervals(
            [
                DateTimeInterval(datetime(2020, 2, 2), datetime(2020, 2, 15)),
                DateTimeInterval(datetime(2020, 2, 23), datetime(2020, 3, 5)),
            ]
        )

        assert datetime(2020, 2, 20) not in dtis

    def test_hollowed_intervals_do_not_contain_hole_edges(self) -> None:
        dtis = DateTimeIntervals(
            [DateTimeInterval(datetime(2020, 2, 2), datetime(2020, 3, 7))]
        )

        dtis = dtis.remove_day(datetime(2020, 2, 20, 7, 12))

        assert datetime(2020, 2, 20) in dtis
        assert datetime(2020, 2, 20, 7, 12) not in dtis
        assert datetime(2020, 2, 21, 7, 12) not in dtis
        assert datetime(2020, 2, 21, 7, 13) in dtis

    def test_intervals_convex_hull_has_no_hole(self) -> None:
        dtis = DateTimeIntervals(
            [
                DateTimeInterval(datetime(2020, 2, 2), datetime(2020, 2, 15)),
                DateTimeInterval(datetime(2020, 2, 23), datetime(2020, 3, 5)),
            ]
        )

        hull = dtis.convex_hull()

        assert isinstance(hull, DateTimeInterval)
        assert datetime(2020, 2, 2, 0, 0, 1) in hull
        assert datetime(2020, 3, 4, 23, 59, 59, 10**6 - 1) in hull
        assert datetime(2020, 2, 17) in hull

    def test_remove_day_can_remove_everything(self) -> None:
        dtis = DateTimeIntervals(
            [
                DateTimeInterval(
                    datetime(2020, 2, 2, 8, 30), datetime(2020, 2, 2, 16, 22)
                )
            ]
        )

        dtis = dtis.remove_day(datetime(2020, 2, 2))

        assert dtis.intervals == []


class TestDayInInterval:
    def test_day_in_interval_returns_only_day_in_interval(self) -> None:
        dii = DayInInterval(
            dates=(d for d in [datetime(2020, 2, 5), datetime(2020, 2, 20)]),
            interval=DateTimeInterval(datetime(2020, 2, 12), datetime(2020, 2, 25)),
        )

        days = list(dii)

        assert days == [datetime(2020, 2, 20)]

    def test_strict_day_in_interval_fails_when_reaching_boundary(self) -> None:
        dii = DayInInterval(
            dates=(d for d in [datetime(2020, 2, 20), datetime(2020, 2, 5)]),
            interval=DateTimeInterval(datetime(2020, 2, 12), datetime(2020, 2, 25)),
            strict=True,
        )

        assert next(dii) == datetime(2020, 2, 20)
        with pytest.raises(ValueError):
            next(dii)
