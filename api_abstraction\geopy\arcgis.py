import logging
from typing import Optional

import geopy
from geopy.geocoders import ArcGIS

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import ApiFail, ApiTimeout, GeocodeApi
from mobility.ir.geo_study import Address, GeoCoordinates


class ArcGISGeocoder(GeocodeApi):
    max_call_per_period = 10

    def __init__(self, token: str, reporter: EventReporter):
        super().__init__(token, reporter)
        self._parse_init_token(token)
        self._geocoder = ArcGIS(
            username=self.username,
            password=self.password,
            referer=self.referer,
        )

    def _parse_init_token(self, token: str) -> None:
        """Parse token from username@referer#password"""
        self.username = token.split("@")[0]
        self.referer = token.split("@")[1].split("#")[0]
        self.password = token.split("@")[1].split("#")[1]

    def _geocode(self, address: str) -> GeoCoordinates:
        try:
            geoloc = self._geocoder.geocode(address, out_fields="*")
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot find geolocalization of {address}")
        return GeoCoordinates(latitude=geoloc.latitude, longitude=geoloc.longitude)

    def _reverse(self, coords: GeoCoordinates) -> Address:
        try:
            geoloc = self._geocoder.reverse((coords.latitude, coords.longitude))
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot reverse geolocalization {coords}")
        return self._extract_address_from_geoloc(geoloc)

    def _geocode_details(self, address: str) -> Address:
        try:
            geoloc = self._geocoder.geocode(address, out_fields="*")
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot find geolocalization of {address}")
        return self._extract_address_from_geoloc(geoloc, address)

    def _extract_address_from_geoloc(
        self, geoloc: geopy.location, origin_address: Optional[str] = None
    ) -> Address:
        if "attributes" not in geoloc.raw:
            raise ApiFail(f"No address field in raw geoloc {geoloc.raw}")
        if "Postal" not in geoloc.raw["attributes"]:
            raise ApiFail(f"No Postal in raw geoloc {geoloc.raw}")
        if "City" not in geoloc.raw["attributes"]:
            raise ApiFail(f"No city name in raw geoloc {geoloc.raw}")
        if float(geoloc.raw["attributes"]["Score"]) < 45:
            logging.warning(
                f"Geolocation might be imprecise: {origin_address} => {geoloc.address} ({geoloc.latitude}, {geoloc.longitude}) | Score: {geoloc.raw['attributes']['Score']}"
            )
        return Address(
            full=geoloc.address if origin_address is None else origin_address,
            normalized=geoloc.address,
            city=geoloc.raw["attributes"]["City"],
            postcode=geoloc.raw["attributes"]["Postal"],
            citycode=geoloc.raw["attributes"]["Postal"],
            coordinates=GeoCoordinates(
                latitude=geoloc.latitude, longitude=geoloc.longitude
            ),
        )
