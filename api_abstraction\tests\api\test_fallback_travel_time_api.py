from typing import Any

import pytest

from api_abstraction.api.fallback_travel_time_api import ApiFail, FallbackTravelTimeApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


class TestFallbackTravelTimeApi:
    def test_inaptitude_fallback_fails_when_empty(self) -> None:
        with pytest.raises(ValueError):
            FallbackTravelTimeApi([])

    def test_inaptitude_fallback_fails_when_single_api(
        self, fake_travel_timer: Any
    ) -> None:
        with pytest.raises(ValueError):
            FallbackTravelTimeApi([fake_travel_timer(22)])

    def test_fallback_returns_first_api_if_success(
        self, fake_travel_timer: Any
    ) -> None:
        api = FallbackTravelTimeApi([fake_travel_timer(2232), fake_travel_timer(24)])

        attributes = api.time(
            GeoCoordinates(0.0, 1.0),
            GeoCoordinates(1.0, 0.0),
            TransportMode.CAR,
            (8, 30),
        )

        assert attributes.duration == 2232

    def test_fallback_returns_second_api_if_first_is_inapt(
        self, fake_travel_timer: Any, inapt_api: Any
    ) -> None:
        api = FallbackTravelTimeApi([inapt_api, fake_travel_timer(9827)])

        attributes = api.time(
            GeoCoordinates(0.0, 1.0),
            GeoCoordinates(1.0, 0.0),
            TransportMode.CAR,
            (8, 30),
        )

        assert attributes.duration == 9827

    def test_fallback_fails_if_api_fails(
        self, fake_travel_timer: Any, failing_api: Any
    ) -> None:
        api = FallbackTravelTimeApi([failing_api, fake_travel_timer(9827)])

        with pytest.raises(ApiFail):
            api.time(
                GeoCoordinates(0.0, 1.0),
                GeoCoordinates(1.0, 0.0),
                TransportMode.CAR,
                (8, 30),
            )

    def test_fallback_returns_nth_api_if_firsts_are_inapt(
        self, fake_travel_timer: Any, inapt_api: Any
    ) -> None:
        api = FallbackTravelTimeApi(
            [
                inapt_api,
                inapt_api,
                inapt_api,
                inapt_api,
                fake_travel_timer(3048),
                fake_travel_timer(4098),
                fake_travel_timer(23),
            ]
        )

        attributes = api.time(
            GeoCoordinates(0.0, 1.0),
            GeoCoordinates(1.0, 0.0),
            TransportMode.CAR,
            (8, 30),
        )

        assert attributes.duration == 3048
