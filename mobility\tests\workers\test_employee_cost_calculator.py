import pytest

from mobility.quantity import euros, yearly
from mobility.workers.employee_cost_calculator import EmployeeCostCalculator


@pytest.fixture
def calculator() -> EmployeeCostCalculator:
    return EmployeeCostCalculator()


class TestEmployeeCostsComputation:
    def test_should_compute_value_of_company_attractiveness_per_employee(
        self, calculator: EmployeeCostCalculator
    ) -> None:
        cost = calculator.compute_company_attractiveness_cost_per_employee()

        assert -788 * euros / yearly <= cost <= -787 * euros / yearly

    def test_should_compute_value_of_absenteeism_per_employee(
        self, calculator: EmployeeCostCalculator
    ) -> None:
        cost = calculator.compute_absenteeism_cost_per_employee()

        assert -268 * euros / yearly < cost < -267 * euros / yearly


class TestComputeSustainableMobilityFee:
    def test_should_compute_positive_cost_for_employer(
        self, calculator: EmployeeCostCalculator
    ) -> None:
        cost = calculator.compute_sustainable_mobility_fee_for_company()

        assert cost == 300 * euros / yearly

    def test_should_compute_negative_cost_for_employee(
        self, calculator: EmployeeCostCalculator
    ) -> None:
        cost = calculator.compute_sustainable_mobility_fee_for_employee()

        assert cost == -300 * euros / yearly
