from datetime import date, datetime, time, timedelta
from typing import Iterator, List, Optional, Union


class DayOfWeek:
    """Iterator on particular days of week

    Computes the date of every specific day of week starting in the future, and alternating
    with days in the past.
    """

    def __init__(
        self,
        start_day: date,
        weekday: int,
        hour: int,
        minute: int,
        week_limit: int = 50,
    ):
        """
        weekday: day of the week (monday: 0)
        """
        days_until_next_week_dow = 7 + weekday - start_day.weekday()
        self.current_dow = start_day + timedelta(days=days_until_next_week_dow)
        self.morning_hour = time(hour=hour, minute=minute)
        self.next_delta = -1
        self.week_limit = week_limit

    def __iter__(self) -> Iterator[datetime]:
        return self

    def __next__(self) -> datetime:
        if abs(self.next_delta) > self.week_limit:
            raise StopIteration()
        result = datetime.combine(self.current_dow, self.morning_hour)
        self.current_dow = self.current_dow + timedelta(days=7 * self.next_delta)
        if self.next_delta > 0:
            self.next_delta = -self.next_delta - 1
        else:
            self.next_delta = -self.next_delta + 1
        return result


class DateTimeInterval:
    def __init__(self, low: Optional[datetime], high: Optional[datetime]):
        self.low = low
        self.high = high

    def __contains__(self, date: datetime) -> bool:
        return (self.low is None or date > self.low) and (
            self.high is None or date < self.high
        )

    def convex_hull(self) -> "DateTimeInterval":
        return self

    def remove_day(self, day: datetime) -> List["DateTimeInterval"]:
        hole_low = day
        hole_high = day + timedelta(days=1)
        if (
            self.low is not None
            and hole_high < self.low
            or self.high is not None
            and hole_low > self.high
        ):
            return [self]
        else:
            if self.low is not None and hole_low < self.low:
                if self.high is not None and hole_high > self.high:
                    return []
                return [DateTimeInterval(hole_high, self.high)]
            if self.high is not None and hole_high > self.high:
                return [DateTimeInterval(self.low, hole_low)]
            return [
                DateTimeInterval(self.low, hole_low),
                DateTimeInterval(hole_high, self.high),
            ]


class DateTimeIntervals:
    def __init__(self, intervals: List[DateTimeInterval]):
        self.intervals = intervals

    def __contains__(self, date: datetime) -> bool:
        return any(date in i for i in self.intervals)

    def convex_hull(self) -> Optional[DateTimeInterval]:
        if len(self.intervals) == 0:
            return None
        low_bounds = [i.low for i in self.intervals]
        high_bounds = [i.high for i in self.intervals]
        some_low_bounds = [b for b in low_bounds if b is not None]
        some_high_bounds = [b for b in high_bounds if b is not None]
        low = None if None in low_bounds else min(some_low_bounds)
        high = None if None in high_bounds else max(some_high_bounds)
        return DateTimeInterval(low, high)

    def remove_day(self, day: datetime) -> "DateTimeIntervals":
        new_intervals = [i.remove_day(day) for i in self.intervals]
        return DateTimeIntervals([i for new_i in new_intervals for i in new_i])


class DayInInterval:
    def __init__(
        self,
        dates: Iterator[datetime],
        interval: Union[DateTimeInterval, DateTimeIntervals],
        strict: bool = False,
    ):
        self.interval = interval
        self.strict = strict
        self.dates = dates

    def __iter__(self) -> Iterator[datetime]:
        return self

    def __next__(self) -> datetime:
        for d in self.dates:
            if d in self.interval:
                return d
            elif self.strict:
                bounds = self.interval.convex_hull()
                if bounds is None or d not in bounds:
                    raise ValueError(f"Date {d} is out of strict interval")
        raise StopIteration()
