import dataclasses
import pickle
from collections import defaultdict
from itertools import product
from typing import Dict, List, Optional, Tuple, TypeVar

import geopy.distance
import networkx

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from internal_api.geo_graph import EdgeDataKind, GeoGraph
from mobility import config
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode
from mobility.quantity import Quantity, gramEC, hours, kilometers, meters, seconds
from mobility.workers.distance_computer import compute_distance

T = TypeVar("T")


class InternalTravelTimer(TravelTimeApi):
    max_call_per_period = 100000
    period_in_seconds = 1.0
    retry_delay_in_seconds = 0.0001

    WALK_SPEED = 4 * kilometers / hours
    BICYCLE_SPEED = 13 * kilometers / hours
    ELECTRIC_BICYCLE_SPEED = 20 * kilometers / hours
    CONNECTOR_CAR_SPEED = 50 * kilometers / hours
    AUTO_CONGESTION_PROPERTY = "aouh"
    CAR_EMISSION = 192 * gramEC / kilometers
    MOTORCYCLE_EMISSION = 165 * gramEC / kilometers
    CARPOOLING_EMISSION = 96 * gramEC / kilometers
    ELECTRIC_CAR_EMISSION = 19.8 * gramEC / kilometers
    ELECTRIC_BICYCLE_EMISSION = 2.23 * gramEC / kilometers

    def __init__(
        self,
        token: str,
        reporter: EventReporter,
        geograph: Optional[GeoGraph] = None,
    ) -> None:
        super().__init__(token, reporter)
        if geograph is None:
            graph_file = config["mobility"].get("network_geograph")
            if graph_file is None:
                raise ValueError(
                    "Internal travel timer requires a graph file,"
                    " set network_geograph value in config file."
                )
            with open(graph_file, "rb") as f:
                self.geograph = pickle.load(f)
        else:
            self.geograph = geograph
        self.car_graph = networkx.subgraph(self.geograph.graph, self.geograph.car_nodes)

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        direct_distance = compute_distance(origin, destination) * 1.4
        connectors = self._compute_graph_connectors(origin, destination, 1)
        journeys = []
        for connector in connectors:
            (
                origin_node,
                origin_distance,
                destination_node,
                destination_distance,
            ) = connector
            journeys.append(
                self._time_in_graph(
                    origin_node,
                    destination_node,
                    origin_distance,
                    destination_distance,
                    direct_distance,
                    mode,
                    arrival_time,
                )
            )
        return min(journeys, key=lambda j: j.duration)

    def _time_in_graph(
        self,
        origin_node: int,
        destination_node: int,
        origin_distance: Quantity,
        destination_distance: Quantity,
        direct_distance: Quantity,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
    ) -> JourneyAttribute:
        connector_distance = origin_distance + destination_distance
        if mode in [
            TransportMode.WALK,
            TransportMode.BICYCLE,
            TransportMode.ELECTRIC_BICYCLE,
        ]:
            mode_speed = {
                TransportMode.WALK: self.WALK_SPEED,
                TransportMode.BICYCLE: self.BICYCLE_SPEED,
                TransportMode.ELECTRIC_BICYCLE: self.ELECTRIC_BICYCLE_SPEED,
            }.get(mode)
            mode_emissions = {
                TransportMode.WALK: 0 * gramEC / meters,
                TransportMode.BICYCLE: 0 * gramEC / meters,
                TransportMode.ELECTRIC_BICYCLE: self.ELECTRIC_BICYCLE_EMISSION,
            }.get(mode)
            if direct_distance < connector_distance:
                distance = direct_distance
            else:
                graph_distance = self._compute_shortest_distance_journey(
                    origin_node, destination_node
                )
                distance = graph_distance + connector_distance
            return JourneyAttribute(
                duration=int((distance / mode_speed) / seconds),
                distance=int(distance / meters),
                emission=int((distance * mode_emissions) / gramEC),
            )
        elif mode in [
            TransportMode.CAR,
            TransportMode.MOTORCYCLE,
            TransportMode.CARPOOLING,
            TransportMode.ELECTRIC_CAR,
        ]:
            if direct_distance < connector_distance:
                distance = direct_distance
                duration = distance / self.CONNECTOR_CAR_SPEED
            else:
                (
                    graph_distance,
                    graph_duration,
                ) = self._compute_shortest_congested_journey(
                    origin_node, destination_node
                )
                connector_duration = connector_distance / self.CONNECTOR_CAR_SPEED
                duration = graph_duration + connector_duration
                distance = graph_distance + connector_distance
            mode_emissions = {
                TransportMode.CAR: self.CAR_EMISSION,
                TransportMode.MOTORCYCLE: self.MOTORCYCLE_EMISSION,
                TransportMode.CARPOOLING: self.CARPOOLING_EMISSION,
                TransportMode.ELECTRIC_CAR: self.ELECTRIC_CAR_EMISSION,
            }.get(mode)
            return JourneyAttribute(
                duration=int(duration / seconds),
                distance=int(distance / meters),
                emission=int(distance * mode_emissions / gramEC),
            )
        elif mode == TransportMode.PUBLIC_TRANSPORT:
            (
                graph_distance,
                graph_duration,
                graph_emissions,
            ) = self._compute_shortest_pt_journey(origin_node, destination_node)
            connector_duration = connector_distance / self.WALK_SPEED
            duration = graph_duration + connector_duration
            distance = graph_distance + connector_distance
            return JourneyAttribute(
                duration=int(duration / seconds),
                distance=int(distance / meters),
                emission=int(graph_emissions / gramEC),
            )
        else:
            raise ApiFail(f"Unhandled mode {mode}")

    def _compute_graph_connectors(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        nb_connectors: int = 1,
    ) -> List[Tuple[int, Quantity, int, Quantity]]:
        origin_connectors = self._closest_graph_nodes(origin, nb_connectors)
        destination_connectors = self._closest_graph_nodes(destination, nb_connectors)
        connectors = []
        for origin_connector, destination_connector in product(
            origin_connectors, destination_connectors
        ):
            o_node_id, o_coords, o_length = origin_connector
            d_node_id, d_coords, d_length = destination_connector
            o_length = self._direct_path_distance(origin, o_coords)
            d_length = self._direct_path_distance(destination, d_coords)
            connectors.append((o_node_id, o_length, d_node_id, d_length))
        return connectors

    def _direct_path_distance(
        self, origin: GeoCoordinates, destination: GeoCoordinates
    ) -> Quantity:
        return (
            geopy.distance.distance(
                (origin.latitude, origin.longitude),
                (destination.latitude, destination.longitude),
            ).m
            * meters
        )

    def _compute_shortest_distance_journey(
        self, origin: int, destination: int
    ) -> Quantity:
        path = self._compute_shortest_distance_path(origin, destination)
        distances: List[float] = self._get_along_path_edges(path, EdgeDataKind.DISTANCE)
        return sum(distances) * meters

    def _compute_shortest_distance_path(
        self, origin: int, destination: int
    ) -> List[int]:
        return networkx.shortest_path(
            self.car_graph, origin, destination, weight=EdgeDataKind.DISTANCE
        )

    def _compute_shortest_pt_journey(
        self, origin: int, destination: int
    ) -> Tuple[Quantity, Quantity, Quantity]:
        path = self._compute_shortest_pt_path(origin, destination)
        durations: List[float] = self._get_along_path_edges(
            path, EdgeDataKind.PT_DURATION
        )
        distances: List[float] = self._get_along_path_edges(path, EdgeDataKind.DISTANCE)
        emissions: List[float] = self._get_along_path_edges(
            path, EdgeDataKind.PT_EMISSION
        )
        return (
            sum(distances) * meters,
            sum(durations) * seconds,
            sum(emissions) * gramEC,
        )

    def _compute_shortest_pt_path(
        self, origin: int, destination: int, force_pt_path: bool = False
    ) -> List[int]:
        if force_pt_path:
            source_mapping = networkx.shortest_path_length(
                self.geograph.graph, source=origin, weight=EdgeDataKind.PT_DURATION
            )
            target_mapping = networkx.shortest_path_length(
                self.geograph.graph, target=destination, weight=EdgeDataKind.PT_DURATION
            )
            pt_nodes = [
                node
                for node in self.geograph.graph.nodes
                if node not in self.geograph.car_nodes
            ]
            fastest_pt_node = min(
                pt_nodes, key=lambda node: source_mapping[node] + target_mapping[node]
            )
            path_to_pt = networkx.shortest_path(
                self.geograph.graph,
                origin,
                fastest_pt_node,
                weight=EdgeDataKind.PT_DURATION,
            )
            path_from_pt = networkx.shortest_path(
                self.geograph.graph,
                fastest_pt_node,
                destination,
                weight=EdgeDataKind.PT_DURATION,
            )
            return path_to_pt[:-1] + path_from_pt
        else:
            return networkx.shortest_path(
                self.geograph.graph,
                origin,
                destination,
                weight=EdgeDataKind.PT_DURATION,
            )

    def _compute_shortest_congested_journey(
        self, origin: int, destination: int
    ) -> Tuple[Quantity, Quantity]:
        path = self._compute_shortest_congested_path(origin, destination)
        durations: List[float] = self._get_along_path_edges(
            path, EdgeDataKind.CONGESTION
        )
        distances: List[float] = self._get_along_path_edges(path, EdgeDataKind.DISTANCE)
        return (
            sum(distances) * meters,
            sum(durations) * seconds,
        )

    def _compute_shortest_congested_path(
        self, origin: int, destination: int
    ) -> List[int]:
        return networkx.shortest_path(
            self.car_graph, origin, destination, weight=EdgeDataKind.CONGESTION
        )

    def _get_along_path_edges(
        self, path: List[int], p: EdgeDataKind, default: Optional[T] = None
    ) -> List[T]:
        return [
            self.geograph.graph[o][d].get(p, default)
            for o, d in zip(path[:-1], path[1:])
        ]

    def _closest_graph_nodes(
        self, position: GeoCoordinates, n_nodes: int = 1
    ) -> List[Tuple[int, GeoCoordinates, Quantity]]:
        n_nodes_to_query = min(n_nodes + 5, len(self.geograph.accessible_nodes))
        _, indexes = self.geograph.accessible_nodes_tree.query(
            (position.latitude, position.longitude), n_nodes_to_query
        )
        closest_nodes = []
        for index in indexes:
            node_id = self.geograph.accessible_nodes[index]
            lat, lon = self.geograph.accessible_latlon[index]
            node_coords = GeoCoordinates(lat, lon)
            node_distance = compute_distance(position, node_coords)
            closest_nodes.append((node_id, node_coords, node_distance))
        return sorted(closest_nodes, key=lambda t: t[2])[:n_nodes]

    def compute_zob(
        self, origin: GeoCoordinates, destination: GeoCoordinates, width: float
    ) -> "Zob":
        origin_node, _, destination_node, _ = self._compute_graph_connectors(
            origin, destination
        )[0]
        return Zob(
            origin_node, destination_node, self.geograph.graph, "duration", width
        )

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        raise ApiInapt("No isochrone capability")

    def compute_detour_costs(
        self, origins: List[GeoCoordinates], destination: GeoCoordinates
    ) -> Dict[int, Dict[int, float]]:
        detours = self.compute_detours(origins, destination)
        detour_costs: Dict[int, Dict[int, float]] = defaultdict(dict)
        for origin, mapping in detours.items():
            duration_to_destination = mapping[-1].duration
            for by_point, journey in mapping.items():
                if by_point == -1:
                    continue
                pickup_cost = journey.duration + detours[by_point][-1].duration
                detour_costs[origin][by_point] = pickup_cost / duration_to_destination
        return detour_costs

    def compute_detours(
        self, origins: List[GeoCoordinates], destination: GeoCoordinates
    ) -> Dict[int, Dict[int, JourneyAttribute]]:
        if len(origins) == 0:
            return {}
        destination_connectors = self._closest_graph_nodes(destination)
        destination_node, _, _ = destination_connectors[0]
        origins_nodes = [self._closest_graph_nodes(o)[0][0] for o in origins]
        to_destination_mapping = networkx.shortest_path_length(
            self.car_graph,
            target=destination_node,
            weight=EdgeDataKind.CONGESTION,
        )
        max_length = max(to_destination_mapping[o] for o in origins_nodes)
        close_nodes = [
            n for n in self.car_graph if to_destination_mapping[n] <= max_length * 1.1
        ]
        cut_graph = networkx.subgraph(self.car_graph, close_nodes)
        detours: Dict[int, Dict[int, JourneyAttribute]] = defaultdict(dict)
        for idx_origin, origin in enumerate(origins_nodes):
            from_origin_mapping = networkx.shortest_path(
                cut_graph, source=origin, weight=EdgeDataKind.CONGESTION
            )
            for idx_by_point, by_point in enumerate(origins_nodes):
                if by_point in from_origin_mapping:
                    path = from_origin_mapping[by_point]
                    duration = (
                        sum(self._get_along_path_edges(path, EdgeDataKind.CONGESTION))
                        * seconds
                    )
                    distance = (
                        sum(self._get_along_path_edges(path, EdgeDataKind.DISTANCE))
                        * meters
                    )
                    detours[idx_origin][idx_by_point] = JourneyAttribute(
                        duration=int(duration / seconds),
                        distance=int(distance / meters),
                        emission=int(distance * self.CAR_EMISSION / gramEC),
                    )
            dest_path = from_origin_mapping[destination_node]
            duration = (
                sum(self._get_along_path_edges(dest_path, EdgeDataKind.CONGESTION))
                * seconds
            )
            distance = (
                sum(self._get_along_path_edges(dest_path, EdgeDataKind.DISTANCE))
                * meters
            )
            detours[idx_origin][-1] = JourneyAttribute(
                duration=int(duration / seconds),
                distance=int(distance / meters),
                emission=int(distance * self.CAR_EMISSION / gramEC),
            )
        return detours


@dataclasses.dataclass
class ZobEdge:
    origin_id: int
    destination_id: int
    origin: Dict
    destination: Dict
    length_before: float
    length: float
    length_after: float


class Zob:
    def __init__(
        self,
        origin: int,
        destination: int,
        graph: networkx.DiGraph,
        optim_param: str,
        width: float,
    ):
        self.from_origin_mapping = networkx.shortest_path_length(
            graph, source=origin, weight=optim_param
        )
        self.to_destination_mapping = networkx.shortest_path_length(
            graph, target=destination, weight=optim_param
        )
        shortest_length = self.from_origin_mapping[destination]
        zob_width = shortest_length * width
        nodes_in_zob = [
            n
            for n in graph.nodes
            if self.from_origin_mapping[n] + self.to_destination_mapping[n] <= zob_width
        ]
        edges_in_zob = networkx.subgraph(graph, nodes_in_zob).edges
        self.zob_edges = []
        self.length_frequencies: Dict[float, int] = defaultdict(int)
        self.nodes_adjacency: Dict[int, int] = defaultdict(int)
        for e in edges_in_zob:
            origin_id = e[0]
            destination_id = e[1]
            length_before = self.from_origin_mapping[origin_id]
            edge_length = graph[origin_id][destination_id][optim_param]
            length_after = self.to_destination_mapping[destination_id]
            through_length = length_before + edge_length + length_after
            self.length_frequencies[through_length] += 1
            if through_length < zob_width:
                self.nodes_adjacency[origin_id] += 1
                self.nodes_adjacency[destination_id] += 1
                self.zob_edges.append(
                    ZobEdge(
                        origin_id=origin_id,
                        destination_id=destination_id,
                        origin=graph.nodes[origin_id],
                        destination=graph.nodes[destination_id],
                        length_before=length_before,
                        length=edge_length,
                        length_after=length_after,
                    )
                )
        freq = [
            (length, self.length_frequencies[length])
            for length in self.length_frequencies
        ]
        for length, f in sorted(freq, key=lambda t: t[1]):
            print(f"length: {length} x{f}")
