import geopy
import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import ApiFail, ApiTimeout
from api_abstraction.geopy.arcgis import ArcGISGeocoder
from mobility.ir.geo_study import Address, GeoCoordinates


@pytest.fixture
def mock_arcgis():
    class fake_arcgis:
        def geocode(
            self, address: str, out_fields: str = "*"
        ) -> geopy.location.Location:
            if address == "failure of the api 34984748 complete fail":
                return None
            elif address == "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg":
                return geopy.location.Location(
                    address="15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
                    point=geopy.point.Point(
                        latitude=49.62049,
                        longitude=6.14654,
                        altitude=0.0,
                    ),
                    raw={
                        "address": "15 Avenue John F. Kennedy,1855, Luxembourg, Weimerskirch",
                        "location": {"x": 6.146191016839, "y": 49.620439573014},
                        "score": 97.28,
                        "attributes": {
                            "Loc_name": "World",
                            "Status": "M",
                            "Score": 97.28,
                            "Match_addr": "15 Avenue John F. Kennedy,1855, Luxembourg, Weimerskirch",
                            "LongLabel": "15 Avenue John F. Kennedy, 1855, Luxembourg, Weimerskirch, LUX",
                            "ShortLabel": "15 Avenue John F. Kennedy",
                            "Addr_type": "PointAddress",
                            "Type": "",
                            "PlaceName": "",
                            "Place_addr": "15 Avenue John F. Kennedy,1855, Luxembourg, Weimerskirch",
                            "Phone": "",
                            "URL": "",
                            "Rank": 20,
                            "AddBldg": "",
                            "AddNum": "15",
                            "AddNumFrom": "",
                            "AddNumTo": "",
                            "AddRange": "",
                            "Side": "",
                            "StPreDir": "",
                            "StPreType": "Avenue",
                            "StName": "John F. Kennedy",
                            "StType": "",
                            "StDir": "",
                            "BldgType": "",
                            "BldgName": "",
                            "LevelType": "",
                            "LevelName": "",
                            "UnitType": "",
                            "UnitName": "",
                            "SubAddr": "",
                            "StAddr": "15 Avenue John F. Kennedy",
                            "Block": "",
                            "Sector": "",
                            "Nbrhd": "Weimerskirch",
                            "District": "Eich",
                            "City": "Luxembourg",
                            "MetroArea": "",
                            "Subregion": "Luxembourg",
                            "Region": "Luxembourg",
                            "RegionAbbr": "",
                            "Territory": "",
                            "Zone": "Luxembourg",
                            "Postal": "1855",
                            "PostalExt": "",
                            "Country": "LUX",
                            "CntryName": "Luxembourg",
                            "LangCode": "FRE",
                            "Distance": 0,
                            "X": 6.146191016839,
                            "Y": 49.620439573014,
                            "DisplayX": 6.146191016839,
                            "DisplayY": 49.620439573014,
                            "Xmin": 6.145191016839,
                            "Xmax": 6.147191016839,
                            "Ymin": 49.619439573014,
                            "Ymax": 49.621439573014,
                            "ExInfo": "A | KIRCHBERG",
                        },
                        "extent": {
                            "xmin": 6.145191016839,
                            "ymin": 49.619439573014,
                            "xmax": 6.147191016839,
                            "ymax": 49.621439573014,
                        },
                    },
                )
            else:
                raise geopy.exc.GeocoderTimedOut("timeout")

    return fake_arcgis()


class TestArcGISGeocoder:
    def test_parse_init_token(mock_arcgis):
        geocoder = ArcGISGeocoder("username@referer#password", EventReporter())
        geocoder._geocoder = mock_arcgis

        geocoder._parse_init_token("<EMAIL>#my_password")

        assert geocoder.username == "mso"
        assert geocoder.referer == "citec.com"
        assert geocoder.password == "my_password"

    def test_geocoder_returns_geocoordinates(self, mock_arcgis):
        geocoder = ArcGISGeocoder("username@referer#password", EventReporter())
        geocoder._geocoder = mock_arcgis

        geoloc = geocoder.geocode(
            "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg"
        )

        assert isinstance(geoloc, GeoCoordinates)

    def test_geocoder_geocodes_an_address(self, mock_arcgis):
        geocoder = ArcGISGeocoder("username@referer#password", EventReporter())
        geocoder._geocoder = mock_arcgis

        geoloc = geocoder.geocode(
            "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg"
        )

        assert geoloc.latitude == 49.62049 and geoloc.longitude == 6.14654

    def test_should_encode_all_the_details_from_address(self, mock_arcgis):
        geocoder = ArcGISGeocoder("username@referer#password", EventReporter())
        geocoder._geocoder = mock_arcgis

        precise_address = geocoder.geocode_details(
            "15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg"
        )

        assert precise_address == Address(
            full="15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
            normalized="15A Avenue John F. Kennedy, L-1855 Kirchberg, Luxembourg",
            city="Luxembourg",
            postcode="1855",
            citycode="1855",
            coordinates=GeoCoordinates(latitude=49.62049, longitude=6.14654),
        )

    def test_geocoder_timeouts_with_api_timeout(self, mock_arcgis):
        geocoder = ArcGISGeocoder("username@referer#password", EventReporter())
        geocoder._geocoder = mock_arcgis
        geocoder.retry_delay_in_seconds = 0

        with pytest.raises(ApiTimeout) as e:
            geocoder.geocode("something something")
        assert e.value.message == "timeout"

    def test_geocoder_fails_on_undefined_address(self, mock_arcgis):
        geocoder = ArcGISGeocoder("username@referer#password", EventReporter())
        geocoder._geocoder = mock_arcgis

        with pytest.raises(ApiFail) as e:
            geocoder.geocode("failure of the api 34984748 complete fail")
        assert (
            e.value.message == "Cannot find geolocalization of failure of the "
            "api 34984748 complete fail"
        )
