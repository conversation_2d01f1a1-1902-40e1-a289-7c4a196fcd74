import csv
import io
import logging
from typing import List, Optional

import geopy
import requests
from geopy.geocoders import BANFrance

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.geocode_api import ApiFail, ApiTimeout, GeocodeApi
from mobility.ir.geo_study import Address, GeoCoordinates


class BanFranceGeocoder(GeocodeApi):
    max_call_per_period = 10

    def __init__(self, token: str, reporter: EventReporter):
        super().__init__(token, reporter)
        self._geocoder = BANFrance()

    def _geocode(self, address: str) -> GeoCoordinates:
        try:
            geoloc = self._geocoder.geocode(address)
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot find geolocalization of {address}")
        return GeoCoordinates(latitude=geoloc.latitude, longitude=geoloc.longitude)

    def _reverse(self, coords: GeoCoordinates) -> Address:
        try:
            geoloc = self._geocoder.reverse((coords.latitude, coords.longitude))
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot reverse geolocalization {coords}")
        return self._extract_address_from_geoloc(geoloc)

    def _geocode_details(self, address: str) -> Address:
        try:
            geoloc = self._geocoder.geocode(address)
        except (geopy.exc.GeocoderTimedOut, geopy.exc.GeocoderUnavailable) as e:
            raise ApiTimeout(str(e))
        except geopy.exc.GeopyError as e:
            raise ApiFail(str(e))
        if geoloc is None:
            raise ApiFail(f"Cannot find geolocalization of {address}")
        return self._extract_address_from_geoloc(geoloc, address)

    def _extract_address_from_geoloc(
        self, geoloc: geopy.location, origin_address: Optional[str] = None
    ) -> Address:
        if "properties" not in geoloc.raw:
            raise ApiFail(f"No properties in raw geoloc {geoloc.raw}")
        if "postcode" not in geoloc.raw["properties"]:
            raise ApiFail(f"No postcode in raw geoloc {geoloc.raw}")
        if "city" not in geoloc.raw["properties"]:
            raise ApiFail(f"No city name in raw geoloc {geoloc.raw}")
        if "citycode" not in geoloc.raw["properties"]:
            raise ApiFail(f"No city code in raw geoloc {geoloc.raw}")
        if float(geoloc.raw["properties"]["score"]) < 0.45:
            logging.warning(
                f"Geolocation might be imprecise: {origin_address} => {geoloc.address} ({geoloc.latitude}, {geoloc.longitude}) | Score: {geoloc.raw['properties']['score']}"
            )
        return Address(
            full=geoloc.address if origin_address is None else origin_address,
            normalized=geoloc.address,
            city=geoloc.raw["properties"]["city"],
            postcode=geoloc.raw["properties"]["postcode"],
            citycode=geoloc.raw["properties"]["citycode"],
            coordinates=GeoCoordinates(
                latitude=geoloc.latitude, longitude=geoloc.longitude
            ),
        )

    def _bulk_reverse(self, coords: List[GeoCoordinates]) -> List[Address]:
        return self._call_ban_france(coords)

    def _call_ban_france(self, coords: List[GeoCoordinates]) -> List[Address]:
        geocoordinates = "lat,lon\n" + "\n".join(
            f"{c.latitude},{c.longitude}" for c in coords
        )
        decoded_content = self._request_api_adresse_csv(geocoordinates)
        cr = csv.DictReader(decoded_content.splitlines(), delimiter=",")
        addresses = []
        for address in cr:
            try:
                addresses.append(
                    Address(
                        full=address["result_label"],
                        normalized=address["result_label"],
                        city=address["result_city"],
                        postcode=address["result_postcode"],
                        citycode=address["result_citycode"],
                        coordinates=GeoCoordinates(
                            latitude=float(address["result_latitude"]),
                            longitude=float(address["result_longitude"]),
                        ),
                    )
                )
            except (KeyError, ValueError):
                continue
        return addresses

    def _request_api_adresse_csv(self, csv_string: str) -> str:
        url = "https://api-adresse.data.gouv.fr/reverse/csv/"
        files_io = {"data": io.StringIO(csv_string)}
        r = requests.post(url, files=files_io)
        return r.content.decode("utf-8")
