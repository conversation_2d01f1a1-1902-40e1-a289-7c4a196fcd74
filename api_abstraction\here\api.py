import json
import logging
from datetime import datetime, time, timedelta
from typing import Dict, Op<PERSON>, <PERSON>ple

import geopandas
from shapely.geometry.base import BaseGeometry

from api_abstraction.api.api import ApiFail, ApiInapt
from api_abstraction.api.date_iterators import DateTimeInterval
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.hypotheses import closest_tuesday_8_30_in_interval
from api_abstraction.api.travel_time_api import JourneyAttribute, TravelTimeApi
from api_abstraction.here.here_api_clients import (
    HereIntermodalAPI,
    HereIsolineAPI,
    HerePublicTransitAPI,
    HereRoutingAPI,
)
from mobility.constants import WALK_SPEED_METERS_PER_SECOND
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.here import (
    HereIntermodalRequestParameters,
    HereIsolineRequestParameters,
    HerePublicTransitRequestParameters,
    HereRoutingRequestParameters,
    RangeType,
    RoutingMode,
    VehicleMode,
)
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode


class HereTravelTimeAPI(TravelTimeApi):
    def __init__(self, token: str, reporter: EventReporter) -> None:
        super().__init__(token, reporter)
        self.logger = logging.getLogger("api")
        self.logger.info("Initialized HERE Travel Time API")
        self.routing_api = HereRoutingAPI(api_key=token)
        self.isoline_api = HereIsolineAPI(api_key=token)
        self.transit_api = HerePublicTransitAPI(api_key=token)
        self.intermodal_api = HereIntermodalAPI(api_key=token)
        self.request_date = self._next_tuesday_8_30()

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        try:
            return self._time_at(origin, destination, mode, arrival_time, max_transfers)
        except Exception as e:
            raise ApiFail(f"HERE Travel Time API request failed: {str(e)}")

    def _time_at(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        arrival_datetime = self._create_arrival_datetime(arrival_time)
        if mode == TransportMode.PUBLIC_TRANSPORT:
            transit_params = HerePublicTransitRequestParameters(
                origin=origin,
                destination=destination,
                mode=mode,
                arrival_time=arrival_datetime,
                max_transfers=max_transfers,
                walk_speed=WALK_SPEED_METERS_PER_SECOND,
            )
            return self.transit_api.compute_route(transit_params)
        elif mode == TransportMode.CAR_PUBLIC_TRANSPORT:
            intermodal_params = HereIntermodalRequestParameters(
                origin=origin,
                destination=destination,
                mode=mode,
                arrival_time=arrival_datetime,
                max_transfers=max_transfers,
                walk_speed=WALK_SPEED_METERS_PER_SECOND,
            )
            return self.intermodal_api.compute_route(intermodal_params)
        elif mode == TransportMode.BICYCLE_PUBLIC_TRANSPORT:
            intermodal_params = HereIntermodalRequestParameters(
                origin=origin,
                destination=destination,
                mode=mode,
                arrival_time=arrival_datetime,
                max_transfers=max_transfers,
                walk_speed=WALK_SPEED_METERS_PER_SECOND,
                vehicle_mode=VehicleMode.BICYCLE,
            )
            return self.intermodal_api.compute_route(intermodal_params)
        else:
            routing_params = HereRoutingRequestParameters(
                origin=origin,
                destination=destination,
                mode=mode,
                arrival_time=arrival_datetime,
                routing_mode=RoutingMode.FAST,
            )
            return self.routing_api.compute_route(routing_params)

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        hour, minute = 8, 30

        if transport_mode in [TransportMode.PUBLIC_TRANSPORT]:
            raise ApiInapt("HERE API does not support public transport isochrones")

        try:
            geometry = self._compute_isochrone_with_arrival_time(
                destination,
                transport_mode,
                boundary,
                hour,
                minute,
            )
            return json.loads(geopandas.GeoSeries(geometry).to_json())["features"][0][
                "geometry"
            ]
        except Exception as e:
            self.logger.warning(f"Failed to compute isochrone: {str(e)}")
            return {}

    def _compute_isochrone_with_arrival_time(
        self,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
        hour: int = 8,
        minute: int = 30,
        second: int = 0,
    ) -> BaseGeometry:
        arrival_datetime = self._create_arrival_datetime((hour, minute))

        isoline_params = HereIsolineRequestParameters(
            center_point=destination,
            arrival_time=arrival_datetime,
            mode=transport_mode,
            range_type=RangeType.TIME,
            range_values=[boundary],
            routing_mode=RoutingMode.FAST,
        )

        return self.isoline_api.compute_isoline(isoline_params)

    def _next_tuesday_8_30(self) -> datetime:
        start = datetime.combine(self._get_today(), time(hour=8, minute=30))
        return closest_tuesday_8_30_in_interval(
            DateTimeInterval(start + timedelta(days=7), None), self._get_today()
        )

    def _create_arrival_datetime(self, arrival_time: Tuple[int, int]) -> datetime:
        hour, minute = arrival_time
        return datetime.combine(self.request_date.date(), time(hour, minute))
