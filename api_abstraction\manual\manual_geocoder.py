from typing import Optional

from api_abstraction.api.geocode_api import GeocodeApi
from mobility.ir.geo_study import Address, GeoCoordinates


class ManualGeocoder(GeocodeApi):
    def _geocode(self, address: str) -> GeoCoordinates:
        print(f"Geocoding address {address}")
        return self._query_coordinates(address)

    def _reverse(self, coords: GeoCoordinates) -> Address:
        return self._query_address(coordinates=coords)

    def _geocode_details(self, address: str) -> Address:
        return self._query_address(address_str=address)

    def _query_address(
        self,
        address_str: Optional[str] = None,
        coordinates: Optional[GeoCoordinates] = None,
    ) -> Address:
        if address_str is not None:
            full_address = address_str
            print(f"Geocoding address {address_str}")
            normalized = self._query_str("normalized address")
            got_coordinates = self._query_coordinates(address_str)
        elif coordinates is not None:
            got_coordinates = coordinates
            print(f"Reverse-geocoding {coordinates}")
            full_address = self._query_str("full address")
            normalized = full_address
        city = self._query_str("city")
        postcode = self._query_str("postcode")
        citycode = self._query_str("citycode")
        return Address(
            full=full_address,
            normalized=normalized,
            city=city,
            postcode=postcode,
            citycode=citycode,
            coordinates=got_coordinates,
        )

    def _query_coordinates(self, address: str) -> GeoCoordinates:
        help_displayed = False
        prefix = "Enter coordinates"
        while True:
            geocoding_str = input(f"{prefix}:\n")
            try:
                return GeoCoordinates.from_string(geocoding_str)
            except ValueError as e:
                print(e)
                if not help_displayed:
                    print("Examples of accepted coordinates format:")
                    print("45.6 5.0\n(45.6, 5.0)\n45.6,5.0")
                    help_displayed = True
                prefix = "Retry entering coordinates"

    def _query_str(self, input_name: str) -> str:
        while True:
            geocoding_str = input(f"Enter {input_name}:\n")
            if geocoding_str == "":
                print("Try again")
            else:
                return geocoding_str
