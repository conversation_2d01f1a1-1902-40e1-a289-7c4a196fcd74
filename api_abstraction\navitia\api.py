import re
from collections import defaultdict
from datetime import datetime, timedelta
from functools import lru_cache
from typing import Dict, List, Optional, Tuple

import geopy.distance
import requests
from geojson import LineString

from api_abstraction.api.api import ApiInapt
from api_abstraction.api.date_iterators import DateTimeInterval
from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.hypotheses import closest_tuesday_8_30_in_interval
from api_abstraction.api.travel_time_api import (
    ApiFail,
    ApiTimeout,
    JourneyAttribute,
    TravelTimeApi,
)
from api_abstraction.navitia.settings import (
    JOURNEY_BICYCLE_PATH,
    JOURNEY_DIRECT_PATH,
    JOURNEY_FORCE_PT_PATH,
    MAX_DURATION_TO_PT,
)
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.territory import Territory
from mobility.ir.transport import TransportMode


class NavitiaTravelTimeAPI(TravelTimeApi):
    journey_url = "https://api.navitia.io/v1/journeys"
    coverage_url = "https://api.navitia.io/v1/coverage"
    isochrone_url = "https://api.navitia.io/v1/coverage"

    def __init__(self, token: str, reporter: EventReporter):
        super().__init__(token, reporter)
        self._request_travel_time = self._call_requests
        self._request_coverage_validity = self._call_requests
        self._request_isochrone = self._call_requests
        self.request_date_param: Dict[TransportMode, Optional[datetime]] = defaultdict(
            lambda: None
        )
        self.minimum_validity_range_duration = timedelta(weeks=8)
        self.unhandled_modes = [
            TransportMode.CAR,
            TransportMode.ELECTRIC_BICYCLE,
            TransportMode.ELECTRIC_CAR,
            TransportMode.CARPOOLING,
            TransportMode.MOTORCYCLE,
        ]

    def _time(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
        max_transfers: Optional[int] = None,
    ) -> JourneyAttribute:
        if mode in self.unhandled_modes:
            raise ApiInapt(f"{mode} not handled")
        params = self._get_params(origin, destination, mode, arrival_time)
        headers = {"Authorization": "{}".format(self.token)}
        try:
            response = self._request_travel_time(self.journey_url, params, headers)
        except requests.Timeout as e:
            raise ApiTimeout(str(e))
        except requests.ConnectionError as e:
            raise ApiFail(str(e))
        inaptitude = self._identify_inaptitude(response)
        if inaptitude is not None:
            raise ApiInapt(inaptitude)
        try:
            chosen_journey = self._choose_fastest_journey(response)
            duration = self._get_journey_total_duration(chosen_journey)
            emission = self._get_journey_emissions(chosen_journey)
            distance = self._get_journey_distance(chosen_journey)
        except KeyError as e:
            raise ApiFail(str(e.args))
        return JourneyAttribute(
            duration=duration,
            distance=distance,
            emission=emission,
        )

    def compute_isochrone(
        self,
        territory: Territory,
        destination: GeoCoordinates,
        transport_mode: TransportMode,
        boundary: int,
    ) -> Dict:
        params = self._get_isochrone_params(destination, [boundary], transport_mode)
        headers = {"Authorization": "{}".format(self.token)}
        region_id = self.get_region_id(territory)
        url = f"{self.isochrone_url}/{region_id}/isochrones"
        try:
            response = self._request_isochrone(url, params, headers)
        except requests.Timeout as e:
            raise ApiTimeout(str(e))
        except requests.ConnectionError as e:
            raise ApiFail(str(e))
        inaptitude = self._identify_inaptitude(response)
        if inaptitude is not None:
            raise ApiInapt(inaptitude)
        try:
            return self._extract_isochrone_geojson(boundary, response)
        except KeyError as e:
            raise ApiFail(str(e))

    @staticmethod
    def get_region_id(territory: Territory) -> Optional[str]:
        return {
            Territory.ARRAS: "fr-ne",
            Territory.ANNONAY: "fr-se",
            Territory.PARIS: "fr-idf",
            Territory.MARSEILLE: "fr-se",
            Territory.LYON: "fr-se",
            Territory.TOULOUSE: "fr-sw",
            Territory.NICE: "fr-se",
            Territory.NANTES: "fr-nw",
            Territory.MONTPELLIER: "fr-se",
            Territory.STRASBOURG: "fr-ne",
            Territory.BORDEAUX: "fr-sw",
            Territory.LILLE: "fr-ne",
            Territory.RENNES: "fr-nw",
            Territory.GRENOBLE: "fr-se",
            Territory.ROUEN: "fr-nw",
            Territory.NANCY: "fr-ne",
            Territory.DIJON: "fr-ne",
            Territory.ORLEANS: "fr-nw",
            Territory.METZ: "fr-ne",
            Territory.AVIGNON: "fr-se",
        }.get(territory)

    @lru_cache(128)
    def _get_coverage_validity_range(
        self, coords: GeoCoordinates
    ) -> Tuple[datetime, datetime]:
        headers = {"Authorization": "{}".format(self.token)}
        url = self.coverage_url + f"/{coords.longitude};{coords.latitude}"
        try:
            coverage = self._request_coverage_validity(url, {}, headers)
        except requests.Timeout as e:
            raise ApiTimeout(str(e))
        except requests.ConnectionError as e:
            raise ApiFail(str(e))
        if "regions" in coverage and len(coverage["regions"]) == 1:
            region = coverage["regions"][0]
        else:
            raise ApiFail(f"Invalid response for coverage at {url}:\n{coverage}")
        date_format = "%Y%m%d"
        start_date = datetime.strptime(region["start_production_date"], date_format)
        end_date = datetime.strptime(region["end_production_date"], date_format)
        return start_date, end_date

    def _get_params(
        self,
        origin: GeoCoordinates,
        destination: GeoCoordinates,
        mode: TransportMode,
        arrival_time: Tuple[int, int],
    ) -> Dict:
        request_date_param = self.request_date_param[mode]
        if request_date_param is None:
            """We choose the date for every subsequent requests once. This could be
            problematic when requesting on different Navitia coverages if they have
            different validity dates, but it allows to not query Navitia for the
            coverage of each origin and destination before computing travel time.
            It's also easier to formulate the hypotheses used during computation with
            a single query date.
            We compute the validity range of the coverage of the destination, which
            is arbitrary-ish: we know that the querying algo has less variety in
            its destinations (many origins to few destinations) and that all the
            destinations have a better chance to be in the same coverage, while the
            first origin could be in a different coverage from all the other origins
            and destinations.
            """
            request_date_param = self._choose_request_date_param(destination, mode)
            self.request_date_param[mode] = request_date_param
        hour, minute = arrival_time
        arrival_date = request_date_param.replace(hour=hour, minute=minute)
        params = {
            "from": f"{origin.longitude};{origin.latitude}",
            "to": f"{destination.longitude};{destination.latitude}",
            "datetime": arrival_date,
            "datetime_represents": "arrival",
            "max_duration_to_pt": f"{MAX_DURATION_TO_PT}",
            "min_nb_journeys": 4,
        }
        params = self._add_journey_mode(params, mode)
        return params

    def _get_isochrone_params(
        self, destination: GeoCoordinates, boundaries: List[int], mode: TransportMode
    ) -> Dict:
        request_date_param = self.request_date_param[mode]
        if request_date_param is None:
            start, end = self._get_coverage_validity_range(destination)
            request_date_param = closest_tuesday_8_30_in_interval(
                DateTimeInterval(start, end), self._get_today()
            )
        params = {
            "to": f"{destination.longitude};{destination.latitude}",
            "datetime": request_date_param,
            "boundary_duration[]": sorted(boundaries),
        }
        return self._add_journey_mode(params, mode)

    def _choose_request_date_param(
        self, coords: GeoCoordinates, mode: TransportMode
    ) -> datetime:
        start, end = self._get_coverage_validity_range(coords)
        if (
            mode is TransportMode.PUBLIC_TRANSPORT
            and abs(end - start) < self.minimum_validity_range_duration
        ):
            raise ApiInapt(
                f"Validity range of coverage ({start}->{end}) for ({coords}) is"
                f" less than the minimum duration ({self.minimum_validity_range_duration})"
            )
        return closest_tuesday_8_30_in_interval(
            DateTimeInterval(start, end),
            self._get_today(),
        )

    @staticmethod
    def _add_journey_mode(params: Dict, mode: TransportMode) -> Dict:
        if mode == TransportMode.WALK or mode == TransportMode.BICYCLE:
            params["direct_path"] = JOURNEY_DIRECT_PATH
        if mode == TransportMode.PUBLIC_TRANSPORT:
            params["direct_path"] = JOURNEY_FORCE_PT_PATH
        if mode == TransportMode.BICYCLE:
            params["direct_path_mode[]"] = JOURNEY_BICYCLE_PATH
        return params

    def _choose_fastest_journey(self, response: Dict) -> Dict:
        self._raise_error_if_no_key(response, "journeys")
        for journey in response["journeys"]:
            if "fastest" in journey["type"]:
                return journey
        # Default to returning the best journey, as determined by Navitia:
        return response["journeys"][0]

    def _get_journey_total_duration(self, journey: Dict) -> int:
        self._raise_error_if_no_key(journey, "durations")
        return journey["durations"]["total"]

    def _get_journey_emissions(self, journey: Dict) -> int:
        self._raise_error_if_no_key(journey, "co2_emission")
        return round(journey["co2_emission"]["value"])

    def _get_journey_distance(self, journey: Dict) -> int:
        self._raise_error_if_no_key(journey, "sections")
        return sum(self._get_section_distance(s) for s in journey["sections"])

    def _get_section_distance(self, section: Dict) -> int:
        if "type" in section and section["type"] == "waiting":
            return 0
        self._raise_error_if_no_key(section, "geojson")
        geo_path = LineString.to_instance(section["geojson"])
        if not geo_path.is_valid or not isinstance(geo_path, LineString):
            raise ApiFail(
                f"Could not extract LineString from {geo_path}:\n {geo_path.errors()}"
            )
        section_distance = 0
        for origin, destination in zip(
            geo_path.coordinates[:-1], geo_path.coordinates[1:]
        ):
            section_distance += geopy.distance.distance(
                (origin[1], origin[0]),
                (destination[1], destination[0]),
            ).m
        return int(section_distance)

    def _extract_isochrone_geojson(self, boundary: int, response: Dict) -> Dict:
        self._raise_error_if_no_key(response, "isochrones")
        for isochrone in response["isochrones"]:
            self._raise_error_if_no_key(isochrone, "max_duration")
            if isochrone["max_duration"] == boundary:
                self._raise_error_if_no_key(isochrone, "geojson")
                return isochrone["geojson"]
        return {}

    @staticmethod
    def _raise_error_if_no_key(response: Dict, key: str) -> None:
        if key not in response:
            if "error" in response:
                raise ApiFail(f'{response["error"]}\n {key} not in {response}')
            else:
                raise ApiFail(f"{key} not in {response}")

    def _identify_inaptitude(self, response: Dict) -> Optional[str]:
        if "error" in response:
            error = response["error"]
            if "message" in error:
                region_boundary_inaptitude = (
                    "^cannot find a region with -?[.0-9]*;-?[.0-9]* and -?[.0-9]*;-?[.0-9]*"
                    " in the same time$"
                )
                coverage_production_period_inaptitude = (
                    "date is not in data production period"
                )
                match_region_boundary = re.match(
                    region_boundary_inaptitude, error["message"]
                )
                if match_region_boundary is not None:
                    return match_region_boundary.string
                elif error["message"] == coverage_production_period_inaptitude:
                    return error["message"]
                else:
                    return None
        return None

    def _call_requests(self, url: str, params: Dict, headers: Dict) -> Dict:
        return requests.get(url, params=params, headers=headers).json()
