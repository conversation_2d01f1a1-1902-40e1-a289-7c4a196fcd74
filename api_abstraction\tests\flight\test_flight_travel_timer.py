from unittest.mock import Mock, patch

import pytest

from api_abstraction.api.event_reporter import EventReporter
from api_abstraction.api.travel_time_api import ApiFail, JourneyAttribute
from api_abstraction.flight.flight_travel_time import FlightComputerApi
from mobility.ir.geo_study import GeoCoordinates
from mobility.ir.transport import TransportMode


class TestTrivialFlightComputerAPI:
    @pytest.fixture
    def reporter(self) -> EventReporter:
        return Mock(spec=EventReporter)

    @pytest.fixture
    def sample_csv_path(self, tmp_path) -> str:
        csv_content = """iata_code,name,municipality,iso_country,coordinates
    CDG,Charles <PERSON>,Paris,FR,"49.009722, 2.547778"
    ORY,Orly,Paris,FR,"48.725278, 2.359444"
    TLS,Blagnac,Toulouse,FR,"43.6291, 1.36382"
    """
        csv_file = tmp_path / "airports.csv"
        csv_file.write_text(csv_content)
        return str(csv_file)

    @pytest.fixture
    def empty_csv_path(self, tmp_path) -> str:
        csv_file = tmp_path / "empty_airports.csv"
        csv_file.write_text("iata_code,name,municipality,iso_country,coordinates\n")
        return str(csv_file)

    def test_time_no_airports_raises_error(
        self, reporter: EventReporter, empty_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, empty_csv_path)

        with pytest.raises(ApiFail, match="No airport found"):
            computer._time(
                GeoCoordinates(0, 0),
                GeoCoordinates(1, 1),
                TransportMode.AIRPLANE,
                (8, 30),
            )

    def test_time_successful_with_ground_transport(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        ground_timer = Mock()
        ground_timer._time.return_value = JourneyAttribute(
            duration=1800, distance=20000, emission=4000
        )
        computer.ground_timer = ground_timer

        result = computer._time(
            GeoCoordinates(0, 0),
            GeoCoordinates(1, 1),
            TransportMode.AIRPLANE,
            (8, 30),
        )

        assert isinstance(result, JourneyAttribute)
        assert result.duration == 1800 + 7200 + 1800
        assert result.distance == 20000 + 0 + 20000
        assert result.emission == 4000 + 0 + 4000

    def test_time_successful_without_ground_transport(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        ground_timer = Mock()
        ground_timer._time.side_effect = ApiFail("Ground transport failed")
        computer.ground_timer = ground_timer

        result = computer._time(
            GeoCoordinates(0, 0),
            GeoCoordinates(1, 1),
            TransportMode.AIRPLANE,
            (8, 30),
        )

        assert isinstance(result, JourneyAttribute)
        assert result.duration == 7200
        assert result.distance == 0
        assert result.emission == 0

    def test_time_uses_fastest_ground_access_mode(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        ground_timer = Mock()
        access_journey_pt = JourneyAttribute(
            duration=2800, distance=20000, emission=4000
        )
        access_journey_car = JourneyAttribute(
            duration=2000, distance=20000, emission=8000
        )
        egress_journey_pt = JourneyAttribute(
            duration=1800, distance=20000, emission=4000
        )
        egress_journey_car = JourneyAttribute(
            duration=2000, distance=20000, emission=8000
        )

        ground_timer._time.side_effect = [
            access_journey_pt,
            access_journey_car,
            egress_journey_pt,
            egress_journey_car,
        ]
        computer.ground_timer = ground_timer

        result = computer._time(
            GeoCoordinates(0, 0),
            GeoCoordinates(1, 1),
            TransportMode.AIRPLANE,
            (8, 30),
        )

        assert isinstance(result, JourneyAttribute)
        assert result.duration == 2000 + 7200 + 1800
        assert result.distance == 20000 + 0 + 20000
        assert result.emission == 8000 + 0 + 4000

    def test_compute_isochrone_not_implemented(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)

        with pytest.raises(NotImplementedError):
            computer.compute_isochrone(
                Mock(), GeoCoordinates(0, 0), TransportMode.AIRPLANE, 3600
            )

    def test_compute_detours_not_implemented(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)

        with pytest.raises(NotImplementedError):
            computer.compute_detours([GeoCoordinates(0, 0)], GeoCoordinates(1, 1))

    def test_get_best_ground_journey_chooses_public_transport_when_faster(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        ground_timer = Mock()
        car_journey = JourneyAttribute(duration=3000, distance=20000, emission=8000)
        pt_journey = JourneyAttribute(duration=1800, distance=15000, emission=3000)
        ground_timer._time.side_effect = [car_journey, pt_journey]
        computer.ground_timer = ground_timer

        result_journey, result_mode = computer._get_best_ground_journey(
            GeoCoordinates(0, 0), GeoCoordinates(1, 1), (8, 30)
        )

        assert result_journey == pt_journey
        assert result_mode == TransportMode.PUBLIC_TRANSPORT

    def test_get_best_ground_journey_chooses_car_when_faster(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        ground_timer = Mock()
        car_journey = JourneyAttribute(duration=1500, distance=20000, emission=8000)
        pt_journey = JourneyAttribute(duration=2500, distance=15000, emission=3000)
        ground_timer._time.side_effect = [car_journey, pt_journey]
        computer.ground_timer = ground_timer

        result_journey, result_mode = computer._get_best_ground_journey(
            GeoCoordinates(0, 0), GeoCoordinates(1, 1), (8, 30)
        )

        assert result_journey == car_journey
        assert result_mode == TransportMode.CAR

    def test_combine_journey_segments_with_all_emissions_present(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        flight = JourneyAttribute(duration=7200, distance=500000, emission=115000)
        access = JourneyAttribute(duration=1800, distance=20000, emission=4000)
        egress = JourneyAttribute(duration=1200, distance=15000, emission=3000)

        result = computer._combine_journey_segments(
            flight, access, TransportMode.CAR, egress, TransportMode.PUBLIC_TRANSPORT
        )

        assert result.duration == 10200
        assert result.distance == 535000
        assert result.emission == 122000

    def test_combine_journey_segments_computes_missing_emissions(
        self, reporter: EventReporter, sample_csv_path: str
    ) -> None:
        computer = FlightComputerApi("token", reporter, sample_csv_path)
        flight = JourneyAttribute(duration=7200, distance=500000, emission=115000)
        access = JourneyAttribute(duration=1800, distance=20000, emission=None)
        egress = JourneyAttribute(duration=1200, distance=15000, emission=None)

        with patch(
            "api_abstraction.flight.flight_travel_time."
            "compute_mode_emission_from_distance"
        ) as mock_compute:
            mock_compute.side_effect = [4500, 2250]

            result = computer._combine_journey_segments(
                flight,
                access,
                TransportMode.CAR,
                egress,
                TransportMode.PUBLIC_TRANSPORT,
            )

            assert result.duration == 10200
            assert result.distance == 535000
            assert result.emission == 121750
            assert mock_compute.call_count == 2
            mock_compute.assert_any_call(TransportMode.CAR, 20000)
            mock_compute.assert_any_call(TransportMode.PUBLIC_TRANSPORT, 15000)
